@echo off
echo ========================================
echo    CORRECTION ET DEMARRAGE TRAINSYSTEM
echo ========================================
echo.

cd /d "%~dp0"

echo 1. Nettoyage complet des processus...
taskkill /F /IM java.exe 2>nul
taskkill /F /IM javaw.exe 2>nul
timeout /t 3 /nobreak >nul

echo 2. Suppression des fichiers temporaires...
if exist "target" rmdir /s /q "target" 2>nul
if exist ".tomcat" rmdir /s /q ".tomcat" 2>nul

echo 3. Nettoyage Maven...
call mvn clean -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors du nettoyage
    pause
    exit /b 1
)

echo 4. Compilation...
call mvn compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)
echo ✅ Compilation réussie

echo 5. Test avec Jetty au lieu de Tomcat...
echo.
echo 🚀 Démarrage avec serveur Jetty...
echo 📍 URL: http://localhost:8080/Train
echo.

start "TrainSystem Jetty Server" cmd /k "echo ========================================= && echo    SERVEUR TRAINSYSTEM JETTY && echo ========================================= && echo. && echo Application: http://localhost:8080/Train && echo Compte test: <EMAIL> / password && echo. && echo Pour arreter: Ctrl+C && echo. && mvn jetty:run -Djetty.port=8080"

echo ⏳ Attente du démarrage (20 secondes)...
timeout /t 20 /nobreak >nul

echo 🌐 Ouverture de l'application...
start http://localhost:8080/Train

echo.
echo ========================================
echo    APPLICATION LANCEE AVEC JETTY !
echo ========================================
echo.
echo ✅ TrainSystem accessible sur : http://localhost:8080/Train
echo 🔑 Compte test : <EMAIL> / password
echo.

pause

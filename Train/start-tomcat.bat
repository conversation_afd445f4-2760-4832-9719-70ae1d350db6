@echo off
echo ========================================
echo    DEMARRAGE TOMCAT POUR TRAINSYSTEM
echo ========================================
echo.

REM Arrêter tous les processus Java existants
echo 1. Arrêt des processus Java existants...
taskkill /F /IM java.exe 2>nul
timeout /t 3 /nobreak >nul

REM Définir les variables d'environnement
set "JAVA_HOME=C:\Program Files\Java\jdk-11"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\OpenJDK\jdk-11"
if not exist "%JAVA_HOME%" set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-11"

REM Rechercher Tomcat
set TOMCAT_FOUND=0
if exist "C:\Program Files\Apache Software Foundation\Tomcat*" (
    for /d %%i in ("C:\Program Files\Apache Software Foundation\Tomcat*") do (
        set "CATALINA_HOME=%%i"
        set TOMCAT_FOUND=1
        goto :found
    )
)

if exist "C:\apache-tomcat*" (
    for /d %%i in ("C:\apache-tomcat*") do (
        set "CATALINA_HOME=%%i"
        set TOMCAT_FOUND=1
        goto :found
    )
)

:found
if %TOMCAT_FOUND%==0 (
    echo ❌ Tomcat non trouvé !
    echo.
    echo Solutions alternatives :
    echo 1. Installer Tomcat depuis https://tomcat.apache.org/
    echo 2. Utiliser un serveur intégré (voir ci-dessous)
    echo.
    goto :embedded
)

echo ✅ Tomcat trouvé : %CATALINA_HOME%
echo ✅ Java Home : %JAVA_HOME%

REM Copier le WAR si nécessaire
if exist "target\Train.war" (
    echo 2. Copie du WAR...
    copy "target\Train.war" "%CATALINA_HOME%\webapps\" >nul
    echo ✅ WAR copié
)

REM Démarrer Tomcat
echo 3. Démarrage de Tomcat...
cd /d "%CATALINA_HOME%\bin"

REM Définir les options JVM
set "JAVA_OPTS=-Xms512m -Xmx1024m -Djava.awt.headless=true"
set "CATALINA_OPTS=-Dfile.encoding=UTF-8"

REM Démarrer en mode console pour voir les logs
echo Démarrage en cours...
call startup.bat

echo.
echo ⏳ Attente du démarrage de Tomcat (30 secondes)...
timeout /t 30 /nobreak >nul

REM Vérifier si Tomcat écoute sur le port 8080
netstat -an | findstr :8080 | findstr LISTENING >nul
if %ERRORLEVEL%==0 (
    echo ✅ Tomcat démarré avec succès !
    echo 🌐 Application disponible : http://localhost:8080/Train
    echo.
    start http://localhost:8080/Train
) else (
    echo ❌ Tomcat ne répond pas sur le port 8080
    echo.
    echo Vérification des logs...
    if exist "%CATALINA_HOME%\logs\catalina.out" (
        echo Dernières lignes du log :
        powershell "Get-Content '%CATALINA_HOME%\logs\catalina.out' | Select-Object -Last 10"
    )
    echo.
    goto :embedded
)

goto :end

:embedded
echo ========================================
echo    SERVEUR INTEGRE ALTERNATIF
echo ========================================
echo.
echo Si Tomcat ne fonctionne pas, utilisons un serveur intégré :
echo.

REM Créer un serveur simple avec Maven
echo Démarrage du serveur intégré Maven...
cd /d "%~dp0"

REM Utiliser le plugin Maven Jetty
echo Tentative avec Jetty...
mvn jetty:run -Djetty.port=8080 -Djetty.host=localhost

:end
echo.
echo Appuyez sur une touche pour fermer...
pause >nul

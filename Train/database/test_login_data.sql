-- ========================================
-- DONNEES DE TEST POUR CONNEXION ET AUTHENTIFICATION
-- Exécutez ce script dans phpMyAdmin pour tester la connexion
-- ========================================

USE train;

-- ========================================
-- UTILISATEURS DE TEST POUR CONNEXION
-- ========================================

-- Supprimer les utilisateurs de test existants
DELETE FROM utilisateurs WHERE email LIKE '%test%' OR email LIKE '%demo%';

-- Ajouter des utilisateurs de test avec différents rôles
-- Mot de passe pour tous : "password" (haché avec BCrypt)
INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, telephone, date_naissance, adresse, ville, code_postal, type_utilisateur, actif) VALUES

-- ADMINISTRATEURS
('Admin', 'Principal', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456789', '1980-01-15', '1 Rue de la Gare', 'Paris', '75001', 'ADMINISTRATEUR', TRUE),
('Super', 'Admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456788', '1975-05-20', '2 Avenue des Trains', 'Lyon', '69001', 'ADMINISTRATEUR', TRUE),

-- EMPLOYES
('Employe', 'Test', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456787', '1985-03-10', '3 Boulevard SNCF', 'Marseille', '13001', 'EMPLOYE', TRUE),
('Agent', 'Gare', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456786', '1990-07-25', '4 Place de la Station', 'Bordeaux', '33000', 'EMPLOYE', TRUE),

-- CLIENTS ACTIFS
('Test', 'Client', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456785', '1992-12-05', '5 Rue du Voyage', 'Toulouse', '31000', 'CLIENT', TRUE),
('Demo', 'User', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456784', '1988-09-18', '6 Avenue des Voyageurs', 'Strasbourg', '67000', 'CLIENT', TRUE),
('Utilisateur', 'Test', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456783', '1995-04-12', '7 Boulevard des Trains', 'Lille', '59000', 'CLIENT', TRUE),

-- CLIENTS AVEC HISTORIQUE
('Dupont', 'Jean', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456782', '1987-11-30', '8 Rue de la République', 'Nice', '06000', 'CLIENT', TRUE),
('Martin', 'Marie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456781', '1993-06-22', '9 Place du Marché', 'Nantes', '44000', 'CLIENT', TRUE),
('Bernard', 'Pierre', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456780', '1991-02-14', '10 Avenue de la Liberté', 'Rennes', '35000', 'CLIENT', TRUE),

-- CLIENT INACTIF (pour tester la validation)
('Inactif', 'Compte', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456779', '1989-08-03', '11 Rue Fermée', 'Montpellier', '34000', 'CLIENT', FALSE),

-- UTILISATEURS AVEC MOTS DE PASSE DIFFÉRENTS (pour tests)
('Test', 'Password123', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcQjyN/L6', '0123456778', '1994-10-17', '12 Rue du Test', 'Dijon', '21000', 'CLIENT', TRUE), -- password: "Test123!"
('Secure', 'User', '<EMAIL>', '$2a$12$EXRkfkdmXn9YxzKzwvzuaecureuFwdg7OpfHFvf6A.JYzTlHK', '0123456777', '1986-01-28', '13 Avenue Sécurisée', 'Reims', '51100', 'CLIENT', TRUE); -- password: "SecurePass2024"

-- ========================================
-- DONNEES DE TEST POUR RESERVATIONS
-- ========================================

-- Supprimer les anciennes réservations de test
DELETE FROM reservations WHERE numero_reservation LIKE 'TEST%';

-- Ajouter des réservations de test pour différents utilisateurs
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, date_reservation, mode_paiement) VALUES

-- Ré<NAME_EMAIL> (ID utilisateur à déterminer)
('TEST001', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), 1, 2, 179.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 5 DAY), 'CARTE_BANCAIRE'),
('TEST002', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), 8, 1, 89.50, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 3 DAY), 'PAYPAL'),
('TEST003', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), 15, 1, 125.00, 'EN_ATTENTE', DATE_SUB(NOW(), INTERVAL 1 DAY), 'CARTE_BANCAIRE'),

-- Ré<NAME_EMAIL>
('TEST004', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), 22, 3, 285.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 7 DAY), 'CARTE_BANCAIRE'),
('TEST005', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), 29, 1, 95.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 2 DAY), 'VIREMENT'),

-- Ré<NAME_EMAIL>
('TEST006', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), 36, 2, 190.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 4 DAY), 'CARTE_BANCAIRE'),
('TEST007', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), 43, 1, 45.00, 'ANNULEE', DATE_SUB(NOW(), INTERVAL 6 DAY), 'CARTE_BANCAIRE'),

-- Ré<NAME_EMAIL>
('TEST008', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), 50, 4, 358.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 10 DAY), 'CARTE_BANCAIRE'),
('TEST009', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), 57, 2, 179.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 1 DAY), 'PAYPAL'),

-- Réservations <NAME_EMAIL>
('TEST010', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), (SELECT id FROM voyages WHERE date_voyage = DATE_ADD(CURDATE(), INTERVAL 2 DAY) LIMIT 1), 1, 89.50, 'CONFIRMEE', NOW(), 'CARTE_BANCAIRE'),
('TEST011', (SELECT id FROM utilisateurs WHERE email = '<EMAIL>'), (SELECT id FROM voyages WHERE date_voyage = DATE_ADD(CURDATE(), INTERVAL 5 DAY) LIMIT 1), 2, 250.00, 'EN_ATTENTE', NOW(), 'CARTE_BANCAIRE');

-- ========================================
-- MISE A JOUR DES STATISTIQUES
-- ========================================

-- Mettre à jour les dernières connexions pour simuler l'activité
UPDATE utilisateurs SET derniere_connexion = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY) WHERE actif = TRUE;

-- Mettre à jour les places réservées dans les voyages
UPDATE voyages v 
SET places_reservees = (
    SELECT COALESCE(SUM(r.nombre_places), 0)
    FROM reservations r 
    WHERE r.voyage_id = v.id AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
),
places_disponibles = (
    SELECT t.nombre_places - COALESCE(SUM(r.nombre_places), 0)
    FROM trajets t
    LEFT JOIN reservations r ON r.voyage_id = v.id AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
    WHERE t.id = v.trajet_id
);

-- ========================================
-- REQUETES DE TEST POUR VALIDATION
-- ========================================

-- Vérifier les utilisateurs créés
SELECT 
    'Utilisateurs de test créés' as info,
    type_utilisateur,
    COUNT(*) as nombre,
    GROUP_CONCAT(email SEPARATOR ', ') as emails
FROM utilisateurs 
WHERE email LIKE '%@train.com' OR email LIKE '%@test.com'
GROUP BY type_utilisateur;

-- Vérifier les réservations de test
SELECT 
    'Réservations de test' as info,
    statut,
    COUNT(*) as nombre,
    SUM(prix_total) as total_ca
FROM reservations 
WHERE numero_reservation LIKE 'TEST%'
GROUP BY statut;

-- Afficher les comptes de test disponibles
SELECT 
    'COMPTES DE TEST DISPONIBLES' as titre,
    CONCAT(prenom, ' ', nom) as nom_complet,
    email,
    type_utilisateur,
    CASE WHEN actif THEN 'ACTIF' ELSE 'INACTIF' END as statut,
    'password' as mot_de_passe_par_defaut
FROM utilisateurs 
WHERE email LIKE '%@train.com' OR email LIKE '%@test.com'
ORDER BY type_utilisateur, email;

COMMIT;

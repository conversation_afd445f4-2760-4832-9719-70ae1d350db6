-- ========================================
-- SCRIPT DE CREATION DES TABLES - TRAINSYSTEM
-- ========================================

-- Utiliser la base de données train
USE train;

-- Supprimer les tables existantes (dans l'ordre des dépendances)
DROP TABLE IF EXISTS reservations;
DROP TABLE IF EXISTS voyages;
DROP TABLE IF EXISTS trajets;
DROP TABLE IF EXISTS utilisateurs;
DROP TABLE IF EXISTS gares;

-- ========================================
-- TABLE: gares
-- ========================================
CREATE TABLE gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    code_gare VARCHAR(10) UNIQUE NOT NULL,
    adresse VARCHAR(255),
    code_postal VARCHAR(10),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    active BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_ville (ville),
    INDEX idx_code_gare (code_gare),
    INDEX idx_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TABLE: utilisateurs
-- ========================================
CREATE TABLE utilisateurs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    mot_de_passe VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    date_naissance DATE,
    adresse VARCHAR(255),
    ville VARCHAR(100),
    code_postal VARCHAR(10),
    type_utilisateur ENUM('CLIENT', 'EMPLOYE', 'ADMINISTRATEUR') DEFAULT 'CLIENT',
    actif BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    derniere_connexion TIMESTAMP NULL,
    
    INDEX idx_email (email),
    INDEX idx_type (type_utilisateur),
    INDEX idx_actif (actif)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TABLE: trajets
-- ========================================
CREATE TABLE trajets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    gare_depart_id BIGINT NOT NULL,
    gare_arrivee_id BIGINT NOT NULL,
    heure_depart TIME NOT NULL,
    heure_arrivee TIME NOT NULL,
    duree_minutes INT GENERATED ALWAYS AS (
        TIME_TO_SEC(heure_arrivee) - TIME_TO_SEC(heure_depart)
    ) STORED,
    prix DECIMAL(10, 2) NOT NULL,
    nombre_places INT NOT NULL DEFAULT 200,
    type_train ENUM('TGV', 'INTERCITES', 'TER', 'OUIGO') DEFAULT 'TER',
    actif BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (gare_depart_id) REFERENCES gares(id) ON DELETE CASCADE,
    FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id) ON DELETE CASCADE,
    
    INDEX idx_gare_depart (gare_depart_id),
    INDEX idx_gare_arrivee (gare_arrivee_id),
    INDEX idx_heure_depart (heure_depart),
    INDEX idx_prix (prix),
    INDEX idx_actif (actif),
    
    CONSTRAINT chk_gares_differentes CHECK (gare_depart_id != gare_arrivee_id),
    CONSTRAINT chk_prix_positif CHECK (prix > 0),
    CONSTRAINT chk_places_positives CHECK (nombre_places > 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TABLE: voyages
-- ========================================
CREATE TABLE voyages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    trajet_id BIGINT NOT NULL,
    date_voyage DATE NOT NULL,
    places_disponibles INT NOT NULL,
    places_reservees INT DEFAULT 0,
    statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE', 'RETARDE') DEFAULT 'PROGRAMME',
    retard_minutes INT DEFAULT 0,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (trajet_id) REFERENCES trajets(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_trajet_date (trajet_id, date_voyage),
    INDEX idx_date_voyage (date_voyage),
    INDEX idx_statut (statut),
    INDEX idx_places_disponibles (places_disponibles),
    
    CONSTRAINT chk_places_coherentes CHECK (places_disponibles >= 0),
    CONSTRAINT chk_places_reservees_positives CHECK (places_reservees >= 0),
    CONSTRAINT chk_retard_positif CHECK (retard_minutes >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TABLE: reservations
-- ========================================
CREATE TABLE reservations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    numero_reservation VARCHAR(20) UNIQUE NOT NULL,
    utilisateur_id BIGINT NOT NULL,
    voyage_id BIGINT NOT NULL,
    nombre_places INT NOT NULL DEFAULT 1,
    prix_total DECIMAL(10, 2) NOT NULL,
    statut ENUM('EN_ATTENTE', 'CONFIRMEE', 'ANNULEE', 'REMBOURSEE') DEFAULT 'EN_ATTENTE',
    date_reservation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_confirmation TIMESTAMP NULL,
    date_annulation TIMESTAMP NULL,
    motif_annulation TEXT,
    mode_paiement ENUM('CARTE_BANCAIRE', 'PAYPAL', 'VIREMENT', 'ESPECES') DEFAULT 'CARTE_BANCAIRE',
    reference_paiement VARCHAR(100),
    
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    FOREIGN KEY (voyage_id) REFERENCES voyages(id) ON DELETE CASCADE,
    
    INDEX idx_numero_reservation (numero_reservation),
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_voyage (voyage_id),
    INDEX idx_statut (statut),
    INDEX idx_date_reservation (date_reservation),
    
    CONSTRAINT chk_nombre_places_positif CHECK (nombre_places > 0),
    CONSTRAINT chk_prix_total_positif CHECK (prix_total > 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TRIGGERS POUR GESTION AUTOMATIQUE
-- ========================================

-- Trigger pour mettre à jour les places lors d'une réservation
DELIMITER //
CREATE TRIGGER tr_reservation_insert 
AFTER INSERT ON reservations
FOR EACH ROW
BEGIN
    IF NEW.statut IN ('CONFIRMEE', 'EN_ATTENTE') THEN
        UPDATE voyages 
        SET places_disponibles = places_disponibles - NEW.nombre_places,
            places_reservees = places_reservees + NEW.nombre_places
        WHERE id = NEW.voyage_id;
    END IF;
END//

-- Trigger pour mettre à jour les places lors de modification d'une réservation
CREATE TRIGGER tr_reservation_update 
AFTER UPDATE ON reservations
FOR EACH ROW
BEGIN
    -- Si le statut change vers annulé
    IF OLD.statut IN ('CONFIRMEE', 'EN_ATTENTE') AND NEW.statut = 'ANNULEE' THEN
        UPDATE voyages 
        SET places_disponibles = places_disponibles + OLD.nombre_places,
            places_reservees = places_reservees - OLD.nombre_places
        WHERE id = OLD.voyage_id;
    END IF;
    
    -- Si le statut change vers confirmé depuis en attente
    IF OLD.statut = 'EN_ATTENTE' AND NEW.statut = 'CONFIRMEE' THEN
        UPDATE reservations 
        SET date_confirmation = CURRENT_TIMESTAMP 
        WHERE id = NEW.id;
    END IF;
    
    -- Si le statut change vers annulé
    IF NEW.statut = 'ANNULEE' AND OLD.statut != 'ANNULEE' THEN
        UPDATE reservations 
        SET date_annulation = CURRENT_TIMESTAMP 
        WHERE id = NEW.id;
    END IF;
END//

DELIMITER ;

-- ========================================
-- VUES UTILES
-- ========================================

-- Vue pour les voyages avec détails complets
CREATE VIEW v_voyages_details AS
SELECT 
    v.id as voyage_id,
    v.date_voyage,
    v.places_disponibles,
    v.places_reservees,
    v.statut as statut_voyage,
    t.id as trajet_id,
    t.heure_depart,
    t.heure_arrivee,
    t.prix,
    t.type_train,
    gd.nom as gare_depart_nom,
    gd.ville as ville_depart,
    gd.code_gare as code_depart,
    ga.nom as gare_arrivee_nom,
    ga.ville as ville_arrivee,
    ga.code_gare as code_arrivee
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE v.statut = 'PROGRAMME' AND t.actif = TRUE;

-- Vue pour les réservations avec détails
CREATE VIEW v_reservations_details AS
SELECT 
    r.id as reservation_id,
    r.numero_reservation,
    r.nombre_places,
    r.prix_total,
    r.statut as statut_reservation,
    r.date_reservation,
    u.nom,
    u.prenom,
    u.email,
    v.date_voyage,
    gd.ville as ville_depart,
    ga.ville as ville_arrivee,
    t.heure_depart,
    t.heure_arrivee
FROM reservations r
JOIN utilisateurs u ON r.utilisateur_id = u.id
JOIN voyages v ON r.voyage_id = v.id
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id;

-- ========================================
-- INDEXES SUPPLEMENTAIRES POUR PERFORMANCE
-- ========================================

-- Index composites pour les recherches fréquentes
CREATE INDEX idx_voyage_date_statut ON voyages(date_voyage, statut);
CREATE INDEX idx_trajet_gares ON trajets(gare_depart_id, gare_arrivee_id);
CREATE INDEX idx_reservation_user_statut ON reservations(utilisateur_id, statut);

COMMIT;

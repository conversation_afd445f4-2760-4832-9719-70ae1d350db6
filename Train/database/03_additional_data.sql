-- ========================================
-- DONNEES SUPPLEMENTAIRES ET OPTIMISATIONS
-- ========================================

USE train;

-- ========================================
-- PROCEDURES STOCKEES UTILES
-- ========================================

DELIMITER //

-- Procédure pour rechercher des voyages
CREATE PROCEDURE sp_rechercher_voyages(
    IN p_ville_depart VARCHAR(100),
    IN p_ville_arrivee VARCHAR(100),
    IN p_date_voyage DATE
)
BEGIN
    SELECT 
        v.id as voyage_id,
        v.date_voyage,
        v.places_disponibles,
        v.statut,
        t.id as trajet_id,
        t.heure_depart,
        t.heure_arrivee,
        t.prix,
        t.type_train,
        gd.nom as gare_depart,
        gd.ville as ville_depart,
        ga.nom as gare_arrivee,
        ga.ville as ville_arrivee,
        TIMEDIFF(t.heure_arrivee, t.heure_depart) as duree
    FROM voyages v
    JOIN trajets t ON v.trajet_id = t.id
    JOIN gares gd ON t.gare_depart_id = gd.id
    JOIN gares ga ON t.gare_arrivee_id = ga.id
    WHERE gd.ville LIKE CONCAT('%', p_ville_depart, '%')
    AND ga.ville LIKE CONCAT('%', p_ville_arrivee, '%')
    AND v.date_voyage = p_date_voyage
    AND v.statut = 'PROGRAMME'
    AND v.places_disponibles > 0
    AND t.actif = TRUE
    ORDER BY t.heure_depart;
END//

-- Procédure pour créer une réservation
CREATE PROCEDURE sp_creer_reservation(
    IN p_utilisateur_id BIGINT,
    IN p_voyage_id BIGINT,
    IN p_nombre_places INT,
    OUT p_numero_reservation VARCHAR(20),
    OUT p_prix_total DECIMAL(10,2)
)
BEGIN
    DECLARE v_prix_unitaire DECIMAL(10,2);
    DECLARE v_places_disponibles INT;
    DECLARE v_numero VARCHAR(20);
    
    -- Vérifier les places disponibles
    SELECT v.places_disponibles, t.prix
    INTO v_places_disponibles, v_prix_unitaire
    FROM voyages v
    JOIN trajets t ON v.trajet_id = t.id
    WHERE v.id = p_voyage_id;
    
    -- Vérifier si assez de places
    IF v_places_disponibles >= p_nombre_places THEN
        -- Générer un numéro de réservation unique
        SET v_numero = CONCAT('RES', LPAD(FLOOR(RAND() * 999999), 6, '0'));
        
        -- Calculer le prix total
        SET p_prix_total = v_prix_unitaire * p_nombre_places;
        
        -- Insérer la réservation
        INSERT INTO reservations (
            numero_reservation, 
            utilisateur_id, 
            voyage_id, 
            nombre_places, 
            prix_total, 
            statut
        ) VALUES (
            v_numero, 
            p_utilisateur_id, 
            p_voyage_id, 
            p_nombre_places, 
            p_prix_total, 
            'EN_ATTENTE'
        );
        
        SET p_numero_reservation = v_numero;
    ELSE
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Pas assez de places disponibles';
    END IF;
END//

-- Procédure pour obtenir les statistiques
CREATE PROCEDURE sp_statistiques_generales()
BEGIN
    SELECT 
        'Utilisateurs actifs' as metric, 
        COUNT(*) as value 
    FROM utilisateurs 
    WHERE actif = TRUE
    
    UNION ALL
    
    SELECT 
        'Réservations confirmées', 
        COUNT(*) 
    FROM reservations 
    WHERE statut = 'CONFIRMEE'
    
    UNION ALL
    
    SELECT 
        'Voyages programmés', 
        COUNT(*) 
    FROM voyages 
    WHERE statut = 'PROGRAMME' AND date_voyage >= CURDATE()
    
    UNION ALL
    
    SELECT 
        'Chiffre d\'affaires (€)', 
        ROUND(SUM(prix_total), 2) 
    FROM reservations 
    WHERE statut = 'CONFIRMEE'
    
    UNION ALL
    
    SELECT 
        'Taux d\'occupation moyen (%)', 
        ROUND(AVG((places_reservees / (places_disponibles + places_reservees)) * 100), 2)
    FROM voyages 
    WHERE date_voyage >= CURDATE();
END//

DELIMITER ;

-- ========================================
-- DONNEES DE TEST SUPPLEMENTAIRES
-- ========================================

-- Ajouter plus d'utilisateurs de test
INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, telephone, type_utilisateur) VALUES
('Leroy', 'Julie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456797', 'CLIENT'),
('Moreau', 'Thomas', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456798', 'CLIENT'),
('Fournier', 'Emma', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456799', 'CLIENT'),
('Girard', 'Lucas', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456800', 'CLIENT'),
('Andre', 'Camille', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456801', 'CLIENT');

-- Ajouter des trajets supplémentaires pour plus de variété
INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places, type_train) VALUES
-- Trajets matinaux
(1, 2, '06:30:00', '11:00:00', 89.50, 200, 'TGV'),     -- Paris -> Lyon (matin)
(1, 3, '07:00:00', '13:30:00', 125.00, 150, 'TGV'),    -- Paris -> Bordeaux (matin)
(2, 1, '06:45:00', '11:15:00', 89.50, 200, 'TGV'),     -- Lyon -> Paris (matin)

-- Trajets de soirée
(1, 2, '19:00:00', '23:30:00', 89.50, 200, 'TGV'),     -- Paris -> Lyon (soir)
(2, 1, '20:15:00', '00:45:00', 89.50, 200, 'TGV'),     -- Lyon -> Paris (soir)
(1, 4, '18:30:00', '03:15:00', 135.00, 180, 'TGV'),    -- Paris -> Marseille (nuit)

-- Trajets OUIGO (low-cost)
(1, 2, '05:45:00', '10:45:00', 45.00, 250, 'OUIGO'),   -- Paris -> Lyon (OUIGO)
(2, 1, '21:30:00', '02:30:00', 45.00, 250, 'OUIGO'),   -- Lyon -> Paris (OUIGO)
(1, 4, '06:15:00', '15:45:00', 65.00, 220, 'OUIGO'),   -- Paris -> Marseille (OUIGO)

-- Trajets TER régionaux
(4, 8, '07:00:00', '10:15:00', 55.00, 120, 'TER'),     -- Marseille -> Nice (matin)
(4, 8, '12:30:00', '15:45:00', 55.00, 120, 'TER'),     -- Marseille -> Nice (midi)
(4, 8, '17:15:00', '20:30:00', 55.00, 120, 'TER'),     -- Marseille -> Nice (soir)
(8, 4, '08:30:00', '11:45:00', 55.00, 120, 'TER'),     -- Nice -> Marseille (matin)
(8, 4, '14:00:00', '17:15:00', 55.00, 120, 'TER'),     -- Nice -> Marseille (après-midi)
(8, 4, '19:45:00', '23:00:00', 55.00, 120, 'TER');     -- Nice -> Marseille (soir)

-- Générer des voyages pour les nouveaux trajets
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles)
SELECT 
    t.id,
    DATE_ADD(CURDATE(), INTERVAL d.day_offset DAY),
    t.nombre_places
FROM trajets t
CROSS JOIN (
    SELECT 0 as day_offset UNION ALL
    SELECT 1 UNION ALL
    SELECT 2 UNION ALL
    SELECT 3 UNION ALL
    SELECT 4 UNION ALL
    SELECT 5 UNION ALL
    SELECT 6 UNION ALL
    SELECT 7 UNION ALL
    SELECT 8 UNION ALL
    SELECT 9
) d
WHERE t.id > (SELECT MAX(id) FROM voyages v2 JOIN trajets t2 ON v2.trajet_id = t2.id)
AND t.actif = TRUE;

-- ========================================
-- DONNEES DE DEMONSTRATION REALISTES
-- ========================================

-- Ajouter des réservations réalistes pour simulation
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, mode_paiement) 
SELECT 
    CONCAT('RES', LPAD(FLOOR(RAND() * 999999), 6, '0')),
    FLOOR(RAND() * 8) + 2,  -- Utilisateurs 2 à 9
    v.id,
    FLOOR(RAND() * 3) + 1,  -- 1 à 3 places
    t.prix * (FLOOR(RAND() * 3) + 1),
    CASE 
        WHEN RAND() < 0.8 THEN 'CONFIRMEE'
        WHEN RAND() < 0.95 THEN 'EN_ATTENTE'
        ELSE 'ANNULEE'
    END,
    CASE 
        WHEN RAND() < 0.7 THEN 'CARTE_BANCAIRE'
        WHEN RAND() < 0.9 THEN 'PAYPAL'
        ELSE 'VIREMENT'
    END
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
WHERE v.date_voyage >= CURDATE()
AND RAND() < 0.3  -- 30% des voyages ont des réservations
LIMIT 50;

-- Mettre à jour les places après les nouvelles réservations
UPDATE voyages v 
SET places_reservees = (
    SELECT COALESCE(SUM(r.nombre_places), 0)
    FROM reservations r 
    WHERE r.voyage_id = v.id 
    AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
),
places_disponibles = (
    SELECT t.nombre_places - COALESCE(SUM(r.nombre_places), 0)
    FROM trajets t
    LEFT JOIN reservations r ON r.voyage_id = v.id AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
    WHERE t.id = v.trajet_id
);

-- ========================================
-- REQUETES DE VERIFICATION
-- ========================================

-- Vérifier les données insérées
SELECT 'Total Gares' as info, COUNT(*) as count FROM gares
UNION ALL
SELECT 'Total Utilisateurs', COUNT(*) FROM utilisateurs
UNION ALL
SELECT 'Total Trajets', COUNT(*) FROM trajets
UNION ALL
SELECT 'Total Voyages', COUNT(*) FROM voyages
UNION ALL
SELECT 'Total Réservations', COUNT(*) FROM reservations
UNION ALL
SELECT 'Réservations Confirmées', COUNT(*) FROM reservations WHERE statut = 'CONFIRMEE'
UNION ALL
SELECT 'Voyages Disponibles', COUNT(*) FROM voyages WHERE date_voyage >= CURDATE() AND places_disponibles > 0;

COMMIT;

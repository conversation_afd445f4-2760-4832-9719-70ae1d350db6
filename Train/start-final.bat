@echo off
echo ========================================
echo    TRAINSYSTEM - DEMARRAGE DEFINITIF
echo ========================================
echo.

cd /d "%~dp0"

echo 1. Arrêt complet de tous les processus Java...
taskkill /F /IM java.exe 2>nul
taskkill /F /IM javaw.exe 2>nul
timeout /t 3 /nobreak >nul

echo 2. Libération de tous les ports...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080') do taskkill /F /PID %%a 2>nul
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8081') do taskkill /F /PID %%a 2>nul
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8082') do taskkill /F /PID %%a 2>nul
timeout /t 2 /nobreak >nul

echo 3. Nettoyage du projet...
call mvn clean -q

echo 4. Compilation...
call mvn compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)
echo ✅ Compilation réussie

echo 5. Recherche d'un port libre...
netstat -ano | findstr :8081 >nul
if %ERRORLEVEL%==0 (
    echo Port 8081 occupé, utilisation du port 8082
    set PORT=8082
) else (
    echo Port 8081 libre
    set PORT=8081
)

echo.
echo 🚀 Démarrage de TrainSystem sur le port %PORT%...
echo 📍 URL: http://localhost:%PORT%/Train
echo 🔑 Compte: <EMAIL> / password
echo.

REM Démarrage avec le port spécifique
start "TrainSystem Server Port %PORT%" cmd /k "echo ========================================= && echo    SERVEUR TRAINSYSTEM PORT %PORT% && echo ========================================= && echo. && echo ✅ Application: http://localhost:%PORT%/Train && echo 🔑 Compte: <EMAIL> / password && echo. && echo ⚠️ Pour arreter: Ctrl+C && echo. && mvn jetty:run -Djetty.port=%PORT%"

echo ⏳ Attente du démarrage (30 secondes)...
timeout /t 30 /nobreak >nul

echo 🌐 Ouverture de l'application...
start http://localhost:%PORT%/Train

echo.
echo ========================================
echo    APPLICATION LANCEE !
echo ========================================
echo.
echo ✅ TrainSystem accessible sur :
echo    👉 http://localhost:%PORT%/Train
echo.
echo 🎯 Fonctionnalités disponibles :
echo    ✅ Page d'accueil moderne
echo    ✅ Inscription utilisateur
echo    ✅ Connexion sécurisée
echo    ✅ Recherche de voyages
echo    ✅ Réservation de billets
echo    ✅ Gestion des réservations
echo.
echo 👤 Compte de test :
echo    📧 Email: <EMAIL>
echo    🔑 Password: password
echo.
echo 🗄️ Données de test :
echo    🏢 8 gares (Paris, Lyon, Marseille...)
echo    🚄 Trajets avec horaires réalistes
echo    📅 Voyages pour 7 jours
echo.
echo 🛑 Pour arrêter : Fermez la fenêtre du serveur
echo.

pause

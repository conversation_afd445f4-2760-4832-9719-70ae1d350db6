@echo off
echo ========================================
echo    DEPLOIEMENT TRAINSYSTEM
echo ========================================
echo.

cd /d "%~dp0"

echo 1. Compilation et packaging...
call mvn clean package -DskipTests
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Echec de la compilation
    pause
    exit /b 1
)
echo ✅ WAR généré avec succès !
echo.

echo 2. Recherche de Tomcat...
set TOMCAT_FOUND=0

REM Recherche dans les emplacements courants
if exist "C:\Program Files\Apache Software Foundation\Tomcat*" (
    for /d %%i in ("C:\Program Files\Apache Software Foundation\Tomcat*") do (
        set "TOMCAT_HOME=%%i"
        set TOMCAT_FOUND=1
        goto :found
    )
)

if exist "C:\apache-tomcat*" (
    for /d %%i in ("C:\apache-tomcat*") do (
        set "TOMCAT_HOME=%%i"
        set TOMCAT_FOUND=1
        goto :found
    )
)

if exist "%CATALINA_HOME%" (
    set "TOMCAT_HOME=%CATALINA_HOME%"
    set TOMCAT_FOUND=1
    goto :found
)

:found
if %TOMCAT_FOUND%==1 (
    echo ✅ Tomcat trouvé: %TOMCAT_HOME%
    
    echo 3. Arrêt de Tomcat...
    if exist "%TOMCAT_HOME%\bin\shutdown.bat" (
        call "%TOMCAT_HOME%\bin\shutdown.bat"
        timeout /t 5 /nobreak >nul
    )
    
    echo 4. Suppression de l'ancienne application...
    if exist "%TOMCAT_HOME%\webapps\Train" (
        rmdir /s /q "%TOMCAT_HOME%\webapps\Train"
    )
    if exist "%TOMCAT_HOME%\webapps\Train.war" (
        del "%TOMCAT_HOME%\webapps\Train.war"
    )
    
    echo 5. Déploiement du nouveau WAR...
    copy "target\Train.war" "%TOMCAT_HOME%\webapps\"
    if %ERRORLEVEL%==0 (
        echo ✅ WAR copié avec succès !
    ) else (
        echo ❌ Erreur lors de la copie du WAR
        pause
        exit /b 1
    )
    
    echo 6. Démarrage de Tomcat...
    if exist "%TOMCAT_HOME%\bin\startup.bat" (
        start "" "%TOMCAT_HOME%\bin\startup.bat"
        echo ✅ Tomcat démarré !
    )
    
    echo.
    echo ========================================
    echo    DEPLOIEMENT TERMINE !
    echo ========================================
    echo.
    echo L'application sera disponible dans quelques secondes à :
    echo 👉 http://localhost:8080/Train
    echo.
    echo Compte administrateur par défaut :
    echo 📧 Email    : <EMAIL>
    echo 🔑 Password : password
    echo.
    echo IMPORTANT: Configurez MySQL avant d'utiliser l'application :
    echo 1. Démarrez MySQL
    echo 2. CREATE DATABASE train;
    echo 3. Exécutez : mysql -u root -p train ^< src/main/resources/database/init.sql
    echo.
    
) else (
    echo ❌ Tomcat non trouvé !
    echo.
    echo Déploiement manuel :
    echo 1. Copiez target\Train.war dans le dossier webapps de Tomcat
    echo 2. Démarrez Tomcat
    echo 3. Accédez à http://localhost:8080/Train
    echo.
)

echo Appuyez sur une touche pour ouvrir l'application dans le navigateur...
pause >nul

start http://localhost:8080/Train

echo.
echo Fin du déploiement.
pause

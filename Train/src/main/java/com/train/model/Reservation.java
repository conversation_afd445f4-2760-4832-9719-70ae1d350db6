package com.train.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entité représentant une réservation
 */
public class Reservation {
    
    private Long id;
    private String numeroReservation;
    private Utilisateur utilisateur;
    private Voyage voyage;
    private int nombrePlaces;
    private BigDecimal prixTotal;
    private StatutReservation statut;
    private LocalDateTime dateReservation;
    private LocalDateTime dateAnnulation;
    private String motifAnnulation;
    
    // Constructeurs
    public Reservation() {}
    
    public Reservation(Utilisateur utilisateur, Voyage voyage, int nombrePlaces) {
        this.utilisateur = utilisateur;
        this.voyage = voyage;
        this.nombrePlaces = nombrePlaces;
        this.prixTotal = voyage.getTrajet().getPrix().multiply(BigDecimal.valueOf(nombrePlaces));
        this.statut = StatutReservation.EN_ATTENTE;
        this.dateReservation = LocalDateTime.now();
        this.numeroReservation = genererNumeroReservation();
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNumeroReservation() {
        return numeroReservation;
    }
    
    public void setNumeroReservation(String numeroReservation) {
        this.numeroReservation = numeroReservation;
    }
    
    public Utilisateur getUtilisateur() {
        return utilisateur;
    }
    
    public void setUtilisateur(Utilisateur utilisateur) {
        this.utilisateur = utilisateur;
    }
    
    public Voyage getVoyage() {
        return voyage;
    }
    
    public void setVoyage(Voyage voyage) {
        this.voyage = voyage;
    }
    
    public int getNombrePlaces() {
        return nombrePlaces;
    }
    
    public void setNombrePlaces(int nombrePlaces) {
        this.nombrePlaces = nombrePlaces;
    }
    
    public BigDecimal getPrixTotal() {
        return prixTotal;
    }
    
    public void setPrixTotal(BigDecimal prixTotal) {
        this.prixTotal = prixTotal;
    }
    
    public StatutReservation getStatut() {
        return statut;
    }
    
    public void setStatut(StatutReservation statut) {
        this.statut = statut;
    }
    
    public LocalDateTime getDateReservation() {
        return dateReservation;
    }
    
    public void setDateReservation(LocalDateTime dateReservation) {
        this.dateReservation = dateReservation;
    }
    
    public LocalDateTime getDateAnnulation() {
        return dateAnnulation;
    }
    
    public void setDateAnnulation(LocalDateTime dateAnnulation) {
        this.dateAnnulation = dateAnnulation;
    }
    
    public String getMotifAnnulation() {
        return motifAnnulation;
    }
    
    public void setMotifAnnulation(String motifAnnulation) {
        this.motifAnnulation = motifAnnulation;
    }
    
    // Méthodes utilitaires
    private String genererNumeroReservation() {
        return "RES" + System.currentTimeMillis();
    }
    
    public void confirmer() {
        this.statut = StatutReservation.CONFIRMEE;
    }
    
    public void annuler(String motif) {
        this.statut = StatutReservation.ANNULEE;
        this.dateAnnulation = LocalDateTime.now();
        this.motifAnnulation = motif;
    }
    
    public boolean peutEtreAnnulee() {
        return statut == StatutReservation.CONFIRMEE && 
               voyage.getDateVoyage().isAfter(LocalDateTime.now().toLocalDate());
    }
    
    public boolean isActive() {
        return statut == StatutReservation.CONFIRMEE || statut == StatutReservation.EN_ATTENTE;
    }
    
    @Override
    public String toString() {
        return "Reservation{" +
                "id=" + id +
                ", numeroReservation='" + numeroReservation + '\'' +
                ", utilisateur=" + utilisateur.getNomComplet() +
                ", voyage=" + voyage.getTrajet().getLibelleTrajet() +
                ", nombrePlaces=" + nombrePlaces +
                ", statut=" + statut +
                '}';
    }
}

package com.train.model;

/**
 * Entité représentant une gare
 */
public class Gare {
    
    private Long id;
    private String nom;
    private String ville;
    private String codeGare;
    private String adresse;
    private String codePostal;
    private boolean active;
    
    // Constructeurs
    public Gare() {}
    
    public Gare(String nom, String ville, String codeGare, String adresse, String codePostal) {
        this.nom = nom;
        this.ville = ville;
        this.codeGare = codeGare;
        this.adresse = adresse;
        this.codePostal = codePostal;
        this.active = true;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNom() {
        return nom;
    }
    
    public void setNom(String nom) {
        this.nom = nom;
    }
    
    public String getVille() {
        return ville;
    }
    
    public void setVille(String ville) {
        this.ville = ville;
    }
    
    public String getCodeGare() {
        return codeGare;
    }
    
    public void setCodeGare(String codeGare) {
        this.codeGare = codeGare;
    }
    
    public String getAdresse() {
        return adresse;
    }
    
    public void setAdresse(String adresse) {
        this.adresse = adresse;
    }
    
    public String getCodePostal() {
        return codePostal;
    }
    
    public void setCodePostal(String codePostal) {
        this.codePostal = codePostal;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    // Méthodes utilitaires
    public String getNomComplet() {
        return nom + " (" + ville + ")";
    }
    
    @Override
    public String toString() {
        return "Gare{" +
                "id=" + id +
                ", nom='" + nom + '\'' +
                ", ville='" + ville + '\'' +
                ", codeGare='" + codeGare + '\'' +
                ", active=" + active +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Gare gare = (Gare) obj;
        return id != null && id.equals(gare.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}

package com.train.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Entité représentant un voyage (instance d'un trajet à une date donnée)
 */
public class Voyage {
    
    private Long id;
    private Trajet trajet;
    private LocalDate dateVoyage;
    private int placesDisponibles;
    private int placesReservees;
    private StatutVoyage statut;
    private LocalDateTime dateCreation;
    
    // Constructeurs
    public Voyage() {}
    
    public Voyage(Trajet trajet, LocalDate dateVoyage) {
        this.trajet = trajet;
        this.dateVoyage = dateVoyage;
        this.placesDisponibles = trajet.getNombrePlaces();
        this.placesReservees = 0;
        this.statut = StatutVoyage.PROGRAMME;
        this.dateCreation = LocalDateTime.now();
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Trajet getTrajet() {
        return trajet;
    }
    
    public void setTrajet(Trajet trajet) {
        this.trajet = trajet;
    }
    
    public LocalDate getDateVoyage() {
        return dateVoyage;
    }
    
    public void setDateVoyage(LocalDate dateVoyage) {
        this.dateVoyage = dateVoyage;
    }
    
    public int getPlacesDisponibles() {
        return placesDisponibles;
    }
    
    public void setPlacesDisponibles(int placesDisponibles) {
        this.placesDisponibles = placesDisponibles;
    }
    
    public int getPlacesReservees() {
        return placesReservees;
    }
    
    public void setPlacesReservees(int placesReservees) {
        this.placesReservees = placesReservees;
    }
    
    public StatutVoyage getStatut() {
        return statut;
    }
    
    public void setStatut(StatutVoyage statut) {
        this.statut = statut;
    }
    
    public LocalDateTime getDateCreation() {
        return dateCreation;
    }
    
    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }
    
    // Méthodes utilitaires
    public boolean hasPlacesDisponibles() {
        return placesDisponibles > 0;
    }
    
    public boolean hasPlacesDisponibles(int nombrePlaces) {
        return placesDisponibles >= nombrePlaces;
    }
    
    public void reserverPlaces(int nombrePlaces) {
        if (hasPlacesDisponibles(nombrePlaces)) {
            this.placesDisponibles -= nombrePlaces;
            this.placesReservees += nombrePlaces;
        } else {
            throw new IllegalStateException("Pas assez de places disponibles");
        }
    }
    
    public void libererPlaces(int nombrePlaces) {
        this.placesDisponibles += nombrePlaces;
        this.placesReservees -= nombrePlaces;
    }
    
    public int getTotalPlaces() {
        return placesDisponibles + placesReservees;
    }
    
    public double getTauxOccupation() {
        int total = getTotalPlaces();
        return total > 0 ? (double) placesReservees / total * 100 : 0;
    }
    
    public boolean isComplet() {
        return placesDisponibles == 0;
    }
    
    public boolean isPasse() {
        return dateVoyage.isBefore(LocalDate.now());
    }
    
    public boolean isAujourdhui() {
        return dateVoyage.equals(LocalDate.now());
    }
    
    public LocalDateTime getDateTimeDepart() {
        return dateVoyage.atTime(trajet.getHeureDepart());
    }
    
    public LocalDateTime getDateTimeArrivee() {
        LocalDateTime arrivee = dateVoyage.atTime(trajet.getHeureArrivee());
        // Si l'heure d'arrivée est avant l'heure de départ, c'est le lendemain
        if (trajet.getHeureArrivee().isBefore(trajet.getHeureDepart())) {
            arrivee = arrivee.plusDays(1);
        }
        return arrivee;
    }
    
    @Override
    public String toString() {
        return "Voyage{" +
                "id=" + id +
                ", trajet=" + trajet.getLibelleTrajet() +
                ", dateVoyage=" + dateVoyage +
                ", placesDisponibles=" + placesDisponibles +
                ", statut=" + statut +
                '}';
    }
}

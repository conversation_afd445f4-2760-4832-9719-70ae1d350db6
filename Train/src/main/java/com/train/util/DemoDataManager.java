package com.train.util;

import com.train.model.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

/**
 * Gestionnaire de données de démonstration pour tester l'application sans base de données
 */
public class DemoDataManager {
    
    private static final Map<Long, Utilisateur> utilisateurs = new HashMap<>();
    private static final Map<Long, Gare> gares = new HashMap<>();
    private static final Map<Long, Trajet> trajets = new HashMap<>();
    private static final Map<Long, Voyage> voyages = new HashMap<>();
    private static final Map<Long, Reservation> reservations = new HashMap<>();
    
    private static long userIdCounter = 1;
    private static long gareIdCounter = 1;
    private static long trajetIdCounter = 1;
    private static long voyageIdCounter = 1;
    private static long reservationIdCounter = 1;
    
    static {
        initializeDemoData();
    }
    
    /**
     * Initialise les données de démonstration
     */
    private static void initializeDemoData() {
        // Créer les gares
        createGares();
        
        // Créer les trajets
        createTrajets();
        
        // Créer les voyages
        createVoyages();
        
        // Créer l'utilisateur admin
        createAdminUser();
        
        System.out.println("✅ Données de démonstration initialisées");
    }
    
    private static void createGares() {
        addGare("Gare Centrale", "Paris", "PAR01", "1 Place de la Gare, Paris", "75001");
        addGare("Gare du Nord", "Lyon", "LYO01", "2 Avenue de la République, Lyon", "69001");
        addGare("Gare Saint-Jean", "Bordeaux", "BOR01", "3 Cours de la Marne, Bordeaux", "33000");
        addGare("Gare Centrale", "Marseille", "MAR01", "4 Boulevard National, Marseille", "13001");
        addGare("Gare SNCF", "Toulouse", "TOU01", "5 Boulevard Pierre Semard, Toulouse", "31000");
        addGare("Gare de l'Est", "Strasbourg", "STR01", "6 Place de la Gare, Strasbourg", "67000");
        addGare("Gare Centrale", "Lille", "LIL01", "7 Place des Buisses, Lille", "59000");
        addGare("Gare Saint-Charles", "Nice", "NIC01", "8 Avenue Thiers, Nice", "06000");
    }
    
    private static void addGare(String nom, String ville, String code, String adresse, String codePostal) {
        Gare gare = new Gare();
        gare.setId(gareIdCounter++);
        gare.setNom(nom);
        gare.setVille(ville);
        gare.setCodeGare(code);
        gare.setAdresse(adresse);
        gare.setCodePostal(codePostal);
        gare.setActive(true);
        gares.put(gare.getId(), gare);
    }
    
    private static void createTrajets() {
        // Paris -> Lyon
        addTrajet(1L, 2L, LocalTime.of(8, 0), LocalTime.of(12, 30), new BigDecimal("89.50"), 200);
        // Lyon -> Paris
        addTrajet(2L, 1L, LocalTime.of(14, 0), LocalTime.of(18, 30), new BigDecimal("89.50"), 200);
        // Paris -> Bordeaux
        addTrajet(1L, 3L, LocalTime.of(9, 15), LocalTime.of(15, 45), new BigDecimal("125.00"), 150);
        // Bordeaux -> Paris
        addTrajet(3L, 1L, LocalTime.of(16, 30), LocalTime.of(23, 0), new BigDecimal("125.00"), 150);
        // Lyon -> Marseille
        addTrajet(2L, 4L, LocalTime.of(7, 30), LocalTime.of(13, 15), new BigDecimal("95.00"), 180);
        // Marseille -> Lyon
        addTrajet(4L, 2L, LocalTime.of(15, 45), LocalTime.of(21, 30), new BigDecimal("95.00"), 180);
        // Paris -> Toulouse
        addTrajet(1L, 5L, LocalTime.of(10, 0), LocalTime.of(17, 20), new BigDecimal("110.00"), 160);
        // Toulouse -> Paris
        addTrajet(5L, 1L, LocalTime.of(18, 0), LocalTime.of(1, 20), new BigDecimal("110.00"), 160);
    }
    
    private static void addTrajet(Long gareDepartId, Long gareArriveeId, LocalTime heureDepart, 
                                 LocalTime heureArrivee, BigDecimal prix, int nombrePlaces) {
        Trajet trajet = new Trajet();
        trajet.setId(trajetIdCounter++);
        trajet.setGareDepart(gares.get(gareDepartId));
        trajet.setGareArrivee(gares.get(gareArriveeId));
        trajet.setHeureDepart(heureDepart);
        trajet.setHeureArrivee(heureArrivee);
        trajet.setPrix(prix);
        trajet.setNombrePlaces(nombrePlaces);
        trajet.setActif(true);
        trajets.put(trajet.getId(), trajet);
    }
    
    private static void createVoyages() {
        LocalDate today = LocalDate.now();
        
        // Créer des voyages pour aujourd'hui et les 7 prochains jours
        for (int day = 0; day < 7; day++) {
            LocalDate dateVoyage = today.plusDays(day);
            
            for (Trajet trajet : trajets.values()) {
                addVoyage(trajet.getId(), dateVoyage, trajet.getNombrePlaces());
            }
        }
    }
    
    private static void addVoyage(Long trajetId, LocalDate dateVoyage, int placesDisponibles) {
        Voyage voyage = new Voyage();
        voyage.setId(voyageIdCounter++);
        voyage.setTrajet(trajets.get(trajetId));
        voyage.setDateVoyage(dateVoyage);
        voyage.setPlacesDisponibles(placesDisponibles);
        voyage.setPlacesReservees(0);
        voyage.setStatut(StatutVoyage.PROGRAMME);
        voyage.setDateCreation(LocalDateTime.now());
        voyages.put(voyage.getId(), voyage);
    }
    
    private static void createAdminUser() {
        Utilisateur admin = new Utilisateur();
        admin.setId(userIdCounter++);
        admin.setNom("Admin");
        admin.setPrenom("Système");
        admin.setEmail("<EMAIL>");
        admin.setMotDePasse(PasswordUtil.hashPassword("password"));
        admin.setTelephone("0123456789");
        admin.setTypeUtilisateur(TypeUtilisateur.ADMINISTRATEUR);
        admin.setDateCreation(LocalDateTime.now());
        admin.setActif(true);
        utilisateurs.put(admin.getId(), admin);
    }
    
    // Méthodes d'accès aux données
    public static List<Utilisateur> getAllUtilisateurs() {
        return new ArrayList<>(utilisateurs.values());
    }
    
    public static List<Gare> getAllGares() {
        return new ArrayList<>(gares.values());
    }
    
    public static List<Trajet> getAllTrajets() {
        return new ArrayList<>(trajets.values());
    }
    
    public static List<Voyage> getAllVoyages() {
        return new ArrayList<>(voyages.values());
    }
    
    public static List<Reservation> getAllReservations() {
        return new ArrayList<>(reservations.values());
    }
    
    public static Optional<Utilisateur> findUtilisateurByEmail(String email) {
        return utilisateurs.values().stream()
                .filter(u -> u.getEmail().equalsIgnoreCase(email))
                .findFirst();
    }
    
    public static Optional<Utilisateur> findUtilisateurById(Long id) {
        return Optional.ofNullable(utilisateurs.get(id));
    }
    
    public static List<Voyage> searchVoyages(String villeDepart, String villeArrivee, LocalDate dateVoyage) {
        return voyages.values().stream()
                .filter(v -> v.getDateVoyage().equals(dateVoyage))
                .filter(v -> v.getTrajet().getGareDepart().getVille().toLowerCase().contains(villeDepart.toLowerCase()))
                .filter(v -> v.getTrajet().getGareArrivee().getVille().toLowerCase().contains(villeArrivee.toLowerCase()))
                .filter(v -> v.getPlacesDisponibles() > 0)
                .filter(v -> v.getStatut() == StatutVoyage.PROGRAMME)
                .sorted((v1, v2) -> v1.getTrajet().getHeureDepart().compareTo(v2.getTrajet().getHeureDepart()))
                .collect(ArrayList::new, (list, item) -> list.add(item), (list1, list2) -> list1.addAll(list2));
    }
    
    public static Optional<Voyage> findVoyageById(Long id) {
        return Optional.ofNullable(voyages.get(id));
    }
    
    public static Utilisateur saveUtilisateur(Utilisateur utilisateur) {
        if (utilisateur.getId() == null) {
            utilisateur.setId(userIdCounter++);
        }
        utilisateurs.put(utilisateur.getId(), utilisateur);
        return utilisateur;
    }
    
    public static Reservation saveReservation(Reservation reservation) {
        if (reservation.getId() == null) {
            reservation.setId(reservationIdCounter++);
        }
        reservations.put(reservation.getId(), reservation);
        
        // Mettre à jour les places du voyage
        Voyage voyage = reservation.getVoyage();
        voyage.setPlacesDisponibles(voyage.getPlacesDisponibles() - reservation.getNombrePlaces());
        voyage.setPlacesReservees(voyage.getPlacesReservees() + reservation.getNombrePlaces());
        
        return reservation;
    }
    
    public static List<Reservation> findReservationsByUserId(Long userId) {
        return reservations.values().stream()
                .filter(r -> r.getUtilisateur().getId().equals(userId))
                .sorted((r1, r2) -> r2.getDateReservation().compareTo(r1.getDateReservation()))
                .collect(ArrayList::new, (list, item) -> list.add(item), (list1, list2) -> list1.addAll(list2));
    }
    
    public static Optional<Reservation> findReservationById(Long id) {
        return Optional.ofNullable(reservations.get(id));
    }
    
    public static boolean hasReservationForVoyage(Long userId, Long voyageId) {
        return reservations.values().stream()
                .anyMatch(r -> r.getUtilisateur().getId().equals(userId) && 
                              r.getVoyage().getId().equals(voyageId) &&
                              (r.getStatut() == StatutReservation.EN_ATTENTE || r.getStatut() == StatutReservation.CONFIRMEE));
    }
    
    public static boolean updateReservation(Reservation reservation) {
        if (reservation.getId() != null && reservations.containsKey(reservation.getId())) {
            reservations.put(reservation.getId(), reservation);
            return true;
        }
        return false;
    }
    
    public static void reset() {
        utilisateurs.clear();
        gares.clear();
        trajets.clear();
        voyages.clear();
        reservations.clear();
        userIdCounter = 1;
        gareIdCounter = 1;
        trajetIdCounter = 1;
        voyageIdCounter = 1;
        reservationIdCounter = 1;
        initializeDemoData();
    }
}

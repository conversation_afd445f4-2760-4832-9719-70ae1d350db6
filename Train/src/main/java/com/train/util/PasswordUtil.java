package com.train.util;

import org.mindrot.jbcrypt.BCrypt;

/**
 * Classe utilitaire pour le hachage et la vérification des mots de passe
 */
public class PasswordUtil {
    
    // Nombre de rounds pour BCrypt (plus élevé = plus sécurisé mais plus lent)
    private static final int BCRYPT_ROUNDS = 12;
    
    /**
     * Hache un mot de passe en utilisant BCrypt
     * @param plainPassword le mot de passe en clair
     * @return le mot de passe haché
     */
    public static String hashPassword(String plainPassword) {
        if (plainPassword == null || plainPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("Le mot de passe ne peut pas être vide");
        }
        return BCrypt.hashpw(plainPassword, BCrypt.gensalt(BCRYPT_ROUNDS));
    }
    
    /**
     * Vérifie si un mot de passe en clair correspond au hash
     * @param plainPassword le mot de passe en clair
     * @param hashedPassword le mot de passe haché
     * @return true si les mots de passe correspondent, false sinon
     */
    public static boolean verifyPassword(String plainPassword, String hashedPassword) {
        if (plainPassword == null || hashedPassword == null) {
            return false;
        }
        try {
            return BCrypt.checkpw(plainPassword, hashedPassword);
        } catch (Exception e) {
            // En cas d'erreur lors de la vérification, retourner false
            return false;
        }
    }
    
    /**
     * Vérifie la force d'un mot de passe
     * @param password le mot de passe à vérifier
     * @return true si le mot de passe est suffisamment fort
     */
    public static boolean isStrongPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }
        
        boolean hasUpper = false;
        boolean hasLower = false;
        boolean hasDigit = false;
        boolean hasSpecial = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (!Character.isLetterOrDigit(c)) {
                hasSpecial = true;
            }
        }
        
        // Le mot de passe doit contenir au moins 3 des 4 types de caractères
        int criteriaCount = 0;
        if (hasUpper) criteriaCount++;
        if (hasLower) criteriaCount++;
        if (hasDigit) criteriaCount++;
        if (hasSpecial) criteriaCount++;
        
        return criteriaCount >= 3;
    }
    
    /**
     * Génère un message d'erreur pour un mot de passe faible
     * @param password le mot de passe à analyser
     * @return message d'erreur ou null si le mot de passe est fort
     */
    public static String getPasswordStrengthMessage(String password) {
        if (password == null || password.trim().isEmpty()) {
            return "Le mot de passe est requis";
        }
        
        if (password.length() < 8) {
            return "Le mot de passe doit contenir au moins 8 caractères";
        }
        
        if (!isStrongPassword(password)) {
            return "Le mot de passe doit contenir au moins 3 des éléments suivants : " +
                   "majuscules, minuscules, chiffres, caractères spéciaux";
        }
        
        return null; // Mot de passe valide
    }
}

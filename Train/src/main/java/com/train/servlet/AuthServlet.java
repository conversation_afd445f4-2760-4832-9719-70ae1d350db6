package com.train.servlet;

import com.train.service.UtilisateurService;
import com.train.service.impl.UtilisateurServiceImpl;
import com.train.model.Utilisateur;
import com.train.model.TypeUtilisateur;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Optional;

/**
 * Servlet pour l'authentification (connexion/déconnexion/inscription)
 */
@WebServlet(name = "AuthServlet", urlPatterns = {"/login", "/logout", "/register"})
public class AuthServlet extends HttpServlet {
    
    private UtilisateurService utilisateurService;
    
    @Override
    public void init() throws ServletException {
        utilisateurService = new UtilisateurServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String path = request.getServletPath();
        
        switch (path) {
            case "/login":
                afficherPageConnexion(request, response);
                break;
            case "/register":
                afficherPageInscription(request, response);
                break;
            case "/logout":
                deconnecter(request, response);
                break;
            default:
                response.sendRedirect(request.getContextPath() + "/home");
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String path = request.getServletPath();
        
        switch (path) {
            case "/login":
                traiterConnexion(request, response);
                break;
            case "/register":
                traiterInscription(request, response);
                break;
            default:
                response.sendRedirect(request.getContextPath() + "/home");
        }
    }
    
    private void afficherPageConnexion(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Si l'utilisateur est déjà connecté, rediriger
        HttpSession session = request.getSession(false);
        if (session != null && session.getAttribute("user") != null) {
            response.sendRedirect(request.getContextPath() + "/home");
            return;
        }
        
        request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
    }
    
    private void afficherPageInscription(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Si l'utilisateur est déjà connecté, rediriger
        HttpSession session = request.getSession(false);
        if (session != null && session.getAttribute("user") != null) {
            response.sendRedirect(request.getContextPath() + "/home");
            return;
        }
        
        request.getRequestDispatcher("/WEB-INF/views/auth/register.jsp").forward(request, response);
    }
    
    private void traiterConnexion(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String email = request.getParameter("email");
        String motDePasse = request.getParameter("motDePasse");
        String redirectUrl = request.getParameter("redirectUrl");
        
        try {
            Optional<Utilisateur> utilisateurOpt = utilisateurService.authentifier(email, motDePasse);
            
            if (utilisateurOpt.isPresent()) {
                Utilisateur utilisateur = utilisateurOpt.get();
                
                // Créer la session
                HttpSession session = request.getSession(true);
                session.setAttribute("user", utilisateur);
                session.setAttribute("userId", utilisateur.getId());
                session.setAttribute("userType", utilisateur.getTypeUtilisateur());
                
                // Message de succès
                session.setAttribute("successMessage", "Connexion réussie ! Bienvenue " + utilisateur.getPrenom());
                
                // Redirection selon le type d'utilisateur ou URL de redirection
                if (redirectUrl != null && !redirectUrl.trim().isEmpty()) {
                    response.sendRedirect(redirectUrl);
                } else if (utilisateur.isAdmin()) {
                    response.sendRedirect(request.getContextPath() + "/admin");
                } else {
                    response.sendRedirect(request.getContextPath() + "/home");
                }
            } else {
                // Échec de l'authentification
                request.setAttribute("errorMessage", "Email ou mot de passe incorrect");
                request.setAttribute("email", email);
                request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
            }
            
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur lors de la connexion : " + e.getMessage());
            request.setAttribute("email", email);
            request.getRequestDispatcher("/WEB-INF/views/auth/login.jsp").forward(request, response);
        }
    }
    
    private void traiterInscription(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String nom = request.getParameter("nom");
        String prenom = request.getParameter("prenom");
        String email = request.getParameter("email");
        String telephone = request.getParameter("telephone");
        String motDePasse = request.getParameter("motDePasse");
        String confirmationMotDePasse = request.getParameter("confirmationMotDePasse");
        
        try {
            // Validation des mots de passe
            if (!motDePasse.equals(confirmationMotDePasse)) {
                throw new IllegalArgumentException("Les mots de passe ne correspondent pas");
            }
            
            // Créer l'utilisateur
            Utilisateur utilisateur = new Utilisateur();
            utilisateur.setNom(nom);
            utilisateur.setPrenom(prenom);
            utilisateur.setEmail(email);
            utilisateur.setTelephone(telephone);
            utilisateur.setTypeUtilisateur(TypeUtilisateur.CLIENT);
            
            // Créer le compte
            Utilisateur nouvelUtilisateur = utilisateurService.creerCompte(utilisateur, motDePasse);
            
            // Créer la session automatiquement
            HttpSession session = request.getSession(true);
            session.setAttribute("user", nouvelUtilisateur);
            session.setAttribute("userId", nouvelUtilisateur.getId());
            session.setAttribute("userType", nouvelUtilisateur.getTypeUtilisateur());
            
            // Message de succès
            session.setAttribute("successMessage", 
                "Compte créé avec succès ! Bienvenue " + nouvelUtilisateur.getPrenom());
            
            response.sendRedirect(request.getContextPath() + "/home");
            
        } catch (Exception e) {
            request.setAttribute("errorMessage", e.getMessage());
            request.setAttribute("nom", nom);
            request.setAttribute("prenom", prenom);
            request.setAttribute("email", email);
            request.setAttribute("telephone", telephone);
            request.getRequestDispatcher("/WEB-INF/views/auth/register.jsp").forward(request, response);
        }
    }
    
    private void deconnecter(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
        
        // Rediriger vers la page d'accueil avec un message
        HttpSession newSession = request.getSession(true);
        newSession.setAttribute("infoMessage", "Vous avez été déconnecté avec succès");
        
        response.sendRedirect(request.getContextPath() + "/home");
    }
}

package com.train.servlet;

import com.train.service.ReservationService;
import com.train.service.VoyageService;
import com.train.service.impl.ReservationServiceImpl;
import com.train.service.impl.VoyageServiceImpl;
import com.train.model.Reservation;
import com.train.model.Voyage;
import com.train.model.Utilisateur;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * Servlet pour la gestion des réservations
 */
@WebServlet(name = "ReservationServlet", urlPatterns = {"/reservation/*"})
public class ReservationServlet extends HttpServlet {
    
    private ReservationService reservationService;
    private VoyageService voyageService;
    
    @Override
    public void init() throws ServletException {
        reservationService = new ReservationServiceImpl();
        voyageService = new VoyageServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        if (pathInfo == null || pathInfo.equals("/")) {
            afficherMesReservations(request, response);
        } else if (pathInfo.startsWith("/create/")) {
            String voyageIdStr = pathInfo.substring("/create/".length());
            afficherPageReservation(request, response, voyageIdStr);
        } else if (pathInfo.startsWith("/details/")) {
            String reservationIdStr = pathInfo.substring("/details/".length());
            afficherDetailsReservation(request, response, reservationIdStr);
        } else if (pathInfo.startsWith("/cancel/")) {
            String reservationIdStr = pathInfo.substring("/cancel/".length());
            afficherPageAnnulation(request, response, reservationIdStr);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        if (pathInfo != null && pathInfo.startsWith("/create/")) {
            String voyageIdStr = pathInfo.substring("/create/".length());
            traiterNouvelleReservation(request, response, voyageIdStr);
        } else if (pathInfo != null && pathInfo.startsWith("/cancel/")) {
            String reservationIdStr = pathInfo.substring("/cancel/".length());
            traiterAnnulationReservation(request, response, reservationIdStr);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    private void afficherMesReservations(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login?redirectUrl=" + 
                java.net.URLEncoder.encode(request.getRequestURI(), "UTF-8"));
            return;
        }
        
        try {
            Long userId = (Long) session.getAttribute("userId");
            List<Reservation> reservations = reservationService.obtenirReservationsUtilisateur(userId);
            
            request.setAttribute("reservations", reservations);
            request.getRequestDispatcher("/WEB-INF/views/reservation/list.jsp").forward(request, response);
            
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur lors de la récupération des réservations : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/reservation/list.jsp").forward(request, response);
        }
    }
    
    private void afficherPageReservation(HttpServletRequest request, HttpServletResponse response, String voyageIdStr) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login?redirectUrl=" + 
                java.net.URLEncoder.encode(request.getRequestURI(), "UTF-8"));
            return;
        }
        
        try {
            Long voyageId = Long.parseLong(voyageIdStr);
            Optional<Voyage> voyageOpt = voyageService.trouverParId(voyageId);
            
            if (!voyageOpt.isPresent()) {
                request.setAttribute("errorMessage", "Voyage non trouvé");
                response.sendRedirect(request.getContextPath() + "/search");
                return;
            }
            
            Voyage voyage = voyageOpt.get();
            
            // Vérifier si l'utilisateur a déjà une réservation pour ce voyage
            Long userId = (Long) session.getAttribute("userId");
            if (reservationService.aDejaReservation(userId, voyageId)) {
                request.setAttribute("errorMessage", "Vous avez déjà une réservation pour ce voyage");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }
            
            request.setAttribute("voyage", voyage);
            request.getRequestDispatcher("/WEB-INF/views/reservation/create.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de voyage invalide");
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/search");
        }
    }
    
    private void afficherDetailsReservation(HttpServletRequest request, HttpServletResponse response, String reservationIdStr) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Optional<Reservation> reservationOpt = reservationService.trouverParId(reservationId);
            
            if (!reservationOpt.isPresent()) {
                request.setAttribute("errorMessage", "Réservation non trouvée");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }
            
            Reservation reservation = reservationOpt.get();
            Long userId = (Long) session.getAttribute("userId");
            
            // Vérifier que la réservation appartient à l'utilisateur connecté
            if (!reservation.getUtilisateur().getId().equals(userId)) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès non autorisé");
                return;
            }
            
            request.setAttribute("reservation", reservation);
            request.getRequestDispatcher("/WEB-INF/views/reservation/details.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/reservation");
        }
    }
    
    private void afficherPageAnnulation(HttpServletRequest request, HttpServletResponse response, String reservationIdStr) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Optional<Reservation> reservationOpt = reservationService.trouverParId(reservationId);
            
            if (!reservationOpt.isPresent()) {
                request.setAttribute("errorMessage", "Réservation non trouvée");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }
            
            Reservation reservation = reservationOpt.get();
            Long userId = (Long) session.getAttribute("userId");
            
            // Vérifier que la réservation appartient à l'utilisateur connecté
            if (!reservation.getUtilisateur().getId().equals(userId)) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès non autorisé");
                return;
            }
            
            // Vérifier que la réservation peut être annulée
            if (!reservationService.peutEtreAnnulee(reservationId)) {
                request.setAttribute("errorMessage", "Cette réservation ne peut pas être annulée");
                response.sendRedirect(request.getContextPath() + "/reservation/details/" + reservationId);
                return;
            }
            
            request.setAttribute("reservation", reservation);
            request.getRequestDispatcher("/WEB-INF/views/reservation/cancel.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/reservation");
        }
    }
    
    private void traiterNouvelleReservation(HttpServletRequest request, HttpServletResponse response, String voyageIdStr) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        try {
            Long voyageId = Long.parseLong(voyageIdStr);
            Long userId = (Long) session.getAttribute("userId");
            
            String nombrePlacesStr = request.getParameter("nombrePlaces");
            int nombrePlaces = Integer.parseInt(nombrePlacesStr);
            
            // Créer la réservation
            Reservation reservation = reservationService.creerReservation(userId, voyageId, nombrePlaces);
            
            // Confirmer automatiquement la réservation (dans un vrai système, il y aurait un processus de paiement)
            reservationService.confirmerReservation(reservation.getId());
            
            session.setAttribute("successMessage", 
                "Réservation créée avec succès ! Numéro de réservation : " + reservation.getNumeroReservation());
            
            response.sendRedirect(request.getContextPath() + "/reservation/details/" + reservation.getId());
            
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "Données invalides");
            afficherPageReservation(request, response, voyageIdStr);
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur lors de la réservation : " + e.getMessage());
            afficherPageReservation(request, response, voyageIdStr);
        }
    }
    
    private void traiterAnnulationReservation(HttpServletRequest request, HttpServletResponse response, String reservationIdStr) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            String motifAnnulation = request.getParameter("motifAnnulation");
            
            if (motifAnnulation == null || motifAnnulation.trim().isEmpty()) {
                motifAnnulation = "Annulation demandée par le client";
            }
            
            boolean success = reservationService.annulerReservation(reservationId, motifAnnulation);
            
            if (success) {
                session.setAttribute("successMessage", "Réservation annulée avec succès");
            } else {
                session.setAttribute("errorMessage", "Erreur lors de l'annulation de la réservation");
            }
            
            response.sendRedirect(request.getContextPath() + "/reservation");
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/reservation");
        }
    }
}

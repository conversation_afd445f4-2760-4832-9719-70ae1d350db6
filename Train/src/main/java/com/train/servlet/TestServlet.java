package com.train.servlet;

import com.train.util.DatabaseConnection;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;

@WebServlet(name = "TestServlet", urlPatterns = {"/test-db", "/test"})
public class TestServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html><head><title>Test TrainSystem</title>");
        out.println("<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>");
        out.println("</head><body>");
        
        out.println("<div class='container my-5'>");
        out.println("<h1 class='text-center'>Test de l'Application TrainSystem</h1>");
        
        // Test connexion DB
        out.println("<div class='card mt-4'>");
        out.println("<div class='card-header'><h3>Test de Connexion Base de Données</h3></div>");
        out.println("<div class='card-body'>");
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            if (conn != null && !conn.isClosed()) {
                out.println("<div class='alert alert-success'>");
                out.println("✅ Connexion réussie !<br>");
                out.println("URL: " + conn.getMetaData().getURL());
                out.println("</div>");
                DatabaseConnection.closeConnection(conn);
            } else {
                out.println("<div class='alert alert-danger'>❌ Échec de la connexion</div>");
            }
        } catch (Exception e) {
            out.println("<div class='alert alert-warning'>");
            out.println("⚠️ Erreur: " + e.getMessage() + "<br><br>");
            out.println("<strong>Pour configurer:</strong><br>");
            out.println("1. Démarrez MySQL<br>");
            out.println("2. CREATE DATABASE train;<br>");
            out.println("3. Exécutez le script init.sql");
            out.println("</div>");
        }
        
        out.println("</div></div>");
        
        // Instructions
        out.println("<div class='card mt-4'>");
        out.println("<div class='card-header'><h3>Instructions</h3></div>");
        out.println("<div class='card-body'>");
        out.println("<div class='alert alert-info'>");
        out.println("<strong>Compte admin par défaut:</strong><br>");
        out.println("Email: <EMAIL><br>");
        out.println("Mot de passe: password");
        out.println("</div>");
        out.println("<a href='" + request.getContextPath() + "/' class='btn btn-primary'>Retour à l'accueil</a>");
        out.println("</div></div>");
        
        out.println("</div></body></html>");
    }
}

package com.train.service.impl;

import com.train.service.VoyageService;
import com.train.model.*;
import com.train.util.DemoDataManager;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation du service voyage en mode démonstration
 */
public class DemoVoyageServiceImpl implements VoyageService {
    
    @Override
    public Voyage creerVoyage(Voyage voyage) {
        validerVoyage(voyage);
        voyage.setDateCreation(LocalDateTime.now());
        voyage.setStatut(StatutVoyage.PROGRAMME);
        voyage.setPlacesReservees(0);
        
        if (voyage.getPlacesDisponibles() == 0) {
            voyage.setPlacesDisponibles(voyage.getTrajet().getNombrePlaces());
        }
        
        // En mode démo, on ne sauvegarde pas vraiment
        return voyage;
    }
    
    @Override
    public Voyage mettreAJourVoyage(Voyage voyage) {
        if (voyage == null || voyage.getId() == null) {
            throw new IllegalArgumentException("Voyage invalide pour la mise à jour");
        }
        validerVoyage(voyage);
        return voyage;
    }
    
    @Override
    public Optional<Voyage> trouverParId(Long id) {
        if (id == null) {
            return Optional.empty();
        }
        return DemoDataManager.findVoyageById(id);
    }
    
    @Override
    public Optional<Voyage> trouverParTrajetEtDate(Long trajetId, LocalDate dateVoyage) {
        if (trajetId == null || dateVoyage == null) {
            return Optional.empty();
        }
        
        return DemoDataManager.getAllVoyages().stream()
                .filter(v -> v.getTrajet().getId().equals(trajetId))
                .filter(v -> v.getDateVoyage().equals(dateVoyage))
                .findFirst();
    }
    
    @Override
    public List<Voyage> rechercherVoyagesDisponibles(String villeDepart, String villeArrivee, LocalDate dateVoyage) {
        if (villeDepart == null || villeArrivee == null || dateVoyage == null) {
            throw new IllegalArgumentException("Tous les paramètres de recherche sont requis");
        }
        
        if (dateVoyage.isBefore(LocalDate.now())) {
            throw new IllegalArgumentException("Impossible de rechercher des voyages dans le passé");
        }
        
        return DemoDataManager.searchVoyages(villeDepart.trim(), villeArrivee.trim(), dateVoyage);
    }
    
    @Override
    public List<Voyage> obtenirVoyagesParDate(LocalDate dateVoyage) {
        if (dateVoyage == null) {
            throw new IllegalArgumentException("La date ne peut pas être null");
        }
        
        return DemoDataManager.getAllVoyages().stream()
                .filter(v -> v.getDateVoyage().equals(dateVoyage))
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<Voyage> obtenirVoyagesParPeriode(LocalDate dateDebut, LocalDate dateFin) {
        if (dateDebut == null || dateFin == null) {
            throw new IllegalArgumentException("Les dates ne peuvent pas être null");
        }
        if (dateDebut.isAfter(dateFin)) {
            throw new IllegalArgumentException("La date de début doit être antérieure à la date de fin");
        }
        
        return DemoDataManager.getAllVoyages().stream()
                .filter(v -> !v.getDateVoyage().isBefore(dateDebut) && !v.getDateVoyage().isAfter(dateFin))
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<Voyage> obtenirVoyagesParStatut(StatutVoyage statut) {
        if (statut == null) {
            throw new IllegalArgumentException("Le statut ne peut pas être null");
        }
        
        return DemoDataManager.getAllVoyages().stream()
                .filter(v -> v.getStatut() == statut)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public boolean reserverPlaces(Long voyageId, int nombrePlaces) {
        if (voyageId == null || nombrePlaces <= 0) {
            return false;
        }
        
        if (!aPlacesDisponibles(voyageId, nombrePlaces)) {
            throw new IllegalStateException("Pas assez de places disponibles");
        }
        
        Optional<Voyage> voyageOpt = DemoDataManager.findVoyageById(voyageId);
        if (voyageOpt.isPresent()) {
            Voyage voyage = voyageOpt.get();
            voyage.setPlacesDisponibles(voyage.getPlacesDisponibles() - nombrePlaces);
            voyage.setPlacesReservees(voyage.getPlacesReservees() + nombrePlaces);
            return true;
        }
        
        return false;
    }
    
    @Override
    public boolean libererPlaces(Long voyageId, int nombrePlaces) {
        if (voyageId == null || nombrePlaces <= 0) {
            return false;
        }
        
        Optional<Voyage> voyageOpt = DemoDataManager.findVoyageById(voyageId);
        if (voyageOpt.isPresent()) {
            Voyage voyage = voyageOpt.get();
            voyage.setPlacesDisponibles(voyage.getPlacesDisponibles() + nombrePlaces);
            voyage.setPlacesReservees(Math.max(0, voyage.getPlacesReservees() - nombrePlaces));
            return true;
        }
        
        return false;
    }
    
    @Override
    public boolean changerStatutVoyage(Long voyageId, StatutVoyage nouveauStatut) {
        if (voyageId == null || nouveauStatut == null) {
            return false;
        }
        
        Optional<Voyage> voyageOpt = DemoDataManager.findVoyageById(voyageId);
        if (voyageOpt.isPresent()) {
            Voyage voyage = voyageOpt.get();
            voyage.setStatut(nouveauStatut);
            return true;
        }
        
        return false;
    }
    
    @Override
    public int genererVoyagesPourTrajet(Long trajetId, LocalDate dateDebut, LocalDate dateFin) {
        // En mode démo, on simule la génération
        return (int) dateDebut.datesUntil(dateFin.plusDays(1)).count();
    }
    
    @Override
    public boolean aPlacesDisponibles(Long voyageId, int nombrePlaces) {
        if (voyageId == null || nombrePlaces <= 0) {
            return false;
        }
        
        Optional<Voyage> voyageOpt = DemoDataManager.findVoyageById(voyageId);
        if (voyageOpt.isPresent()) {
            Voyage voyage = voyageOpt.get();
            return voyage.getPlacesDisponibles() >= nombrePlaces && 
                   voyage.getStatut() == StatutVoyage.PROGRAMME;
        }
        
        return false;
    }
    
    @Override
    public double obtenirTauxOccupation(Long voyageId) {
        if (voyageId == null) {
            return 0.0;
        }
        
        Optional<Voyage> voyageOpt = DemoDataManager.findVoyageById(voyageId);
        if (voyageOpt.isPresent()) {
            Voyage voyage = voyageOpt.get();
            int totalPlaces = voyage.getPlacesDisponibles() + voyage.getPlacesReservees();
            if (totalPlaces > 0) {
                return (double) voyage.getPlacesReservees() / totalPlaces * 100.0;
            }
        }
        
        return 0.0;
    }
    
    @Override
    public boolean supprimerVoyage(Long voyageId) {
        if (voyageId == null) {
            return false;
        }
        
        Optional<Voyage> voyageOpt = DemoDataManager.findVoyageById(voyageId);
        if (voyageOpt.isPresent()) {
            Voyage voyage = voyageOpt.get();
            
            if (voyage.getPlacesReservees() > 0) {
                throw new IllegalStateException("Impossible de supprimer un voyage avec des réservations");
            }
            
            if (voyage.getDateVoyage().isBefore(LocalDate.now()) || 
                voyage.getStatut() == StatutVoyage.EN_COURS) {
                throw new IllegalStateException("Impossible de supprimer un voyage passé ou en cours");
            }
            
            // En mode démo, on simule la suppression
            return true;
        }
        
        return false;
    }
    
    @Override
    public void validerVoyage(Voyage voyage) {
        if (voyage == null) {
            throw new IllegalArgumentException("Le voyage ne peut pas être null");
        }
        
        if (voyage.getTrajet() == null) {
            throw new IllegalArgumentException("Le trajet est requis");
        }
        
        if (voyage.getDateVoyage() == null) {
            throw new IllegalArgumentException("La date du voyage est requise");
        }
        
        if (voyage.getDateVoyage().isBefore(LocalDate.now())) {
            throw new IllegalArgumentException("La date du voyage ne peut pas être dans le passé");
        }
        
        if (voyage.getPlacesDisponibles() < 0) {
            throw new IllegalArgumentException("Le nombre de places disponibles ne peut pas être négatif");
        }
        
        if (voyage.getPlacesReservees() < 0) {
            throw new IllegalArgumentException("Le nombre de places réservées ne peut pas être négatif");
        }
    }
}

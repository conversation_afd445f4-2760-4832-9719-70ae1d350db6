package com.train.service.impl;

import com.train.service.UtilisateurService;
import com.train.dao.UtilisateurDAO;
import com.train.dao.impl.UtilisateurDAOImpl;
import com.train.model.Utilisateur;
import com.train.model.TypeUtilisateur;
import com.train.util.PasswordUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation du service de gestion des utilisateurs
 */
public class UtilisateurServiceImpl implements UtilisateurService {
    
    private final UtilisateurDAO utilisateurDAO;
    
    public UtilisateurServiceImpl() {
        this.utilisateurDAO = new UtilisateurDAOImpl();
    }
    
    public UtilisateurServiceImpl(UtilisateurDAO utilisateurDAO) {
        this.utilisateurDAO = utilisateurDAO;
    }
    
    @Override
    public Utilisateur creerCompte(Utilisateur utilisateur, String motDePasseClair) {
        // Validation des données
        validerUtilisateur(utilisateur, motDePasseClair);
        
        // Vérifier que l'email n'existe pas déjà
        if (emailExiste(utilisateur.getEmail())) {
            throw new IllegalArgumentException("Un compte avec cet email existe déjà");
        }
        
        // Hacher le mot de passe
        String motDePasseHache = PasswordUtil.hashPassword(motDePasseClair);
        utilisateur.setMotDePasse(motDePasseHache);
        
        // Définir les valeurs par défaut
        utilisateur.setDateCreation(LocalDateTime.now());
        utilisateur.setActif(true);
        
        // Si aucun type n'est défini, définir comme CLIENT
        if (utilisateur.getTypeUtilisateur() == null) {
            utilisateur.setTypeUtilisateur(TypeUtilisateur.CLIENT);
        }
        
        return utilisateurDAO.save(utilisateur);
    }
    
    @Override
    public Optional<Utilisateur> authentifier(String email, String motDePasse) {
        if (email == null || email.trim().isEmpty() || motDePasse == null || motDePasse.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return utilisateurDAO.authenticate(email.trim().toLowerCase(), motDePasse);
    }
    
    @Override
    public Utilisateur mettreAJourProfil(Utilisateur utilisateur) {
        if (utilisateur == null || utilisateur.getId() == null) {
            throw new IllegalArgumentException("Utilisateur invalide pour la mise à jour");
        }
        
        // Vérifier que l'utilisateur existe
        Optional<Utilisateur> existant = utilisateurDAO.findById(utilisateur.getId());
        if (!existant.isPresent()) {
            throw new IllegalArgumentException("Utilisateur non trouvé");
        }
        
        // Valider les nouvelles données (sans mot de passe)
        validerUtilisateur(utilisateur, null);
        
        // Vérifier que l'email n'est pas utilisé par un autre utilisateur
        Optional<Utilisateur> autreUtilisateur = utilisateurDAO.findByEmail(utilisateur.getEmail());
        if (autreUtilisateur.isPresent() && !autreUtilisateur.get().getId().equals(utilisateur.getId())) {
            throw new IllegalArgumentException("Cet email est déjà utilisé par un autre compte");
        }
        
        return utilisateurDAO.update(utilisateur);
    }
    
    @Override
    public boolean changerMotDePasse(Long utilisateurId, String ancienMotDePasse, String nouveauMotDePasse) {
        if (utilisateurId == null || ancienMotDePasse == null || nouveauMotDePasse == null) {
            return false;
        }
        
        // Vérifier que l'utilisateur existe
        Optional<Utilisateur> utilisateurOpt = utilisateurDAO.findById(utilisateurId);
        if (!utilisateurOpt.isPresent()) {
            return false;
        }
        
        Utilisateur utilisateur = utilisateurOpt.get();
        
        // Vérifier l'ancien mot de passe
        if (!PasswordUtil.verifyPassword(ancienMotDePasse, utilisateur.getMotDePasse())) {
            return false;
        }
        
        // Valider le nouveau mot de passe
        String messageErreur = PasswordUtil.getPasswordStrengthMessage(nouveauMotDePasse);
        if (messageErreur != null) {
            throw new IllegalArgumentException(messageErreur);
        }
        
        // Hacher et sauvegarder le nouveau mot de passe
        String nouveauMotDePasseHache = PasswordUtil.hashPassword(nouveauMotDePasse);
        return utilisateurDAO.changePassword(utilisateurId, nouveauMotDePasseHache);
    }
    
    @Override
    public Optional<Utilisateur> trouverParId(Long id) {
        if (id == null) {
            return Optional.empty();
        }
        return utilisateurDAO.findById(id);
    }
    
    @Override
    public Optional<Utilisateur> trouverParEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return Optional.empty();
        }
        return utilisateurDAO.findByEmail(email.trim().toLowerCase());
    }
    
    @Override
    public boolean emailExiste(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return utilisateurDAO.existsByEmail(email.trim().toLowerCase());
    }
    
    @Override
    public List<Utilisateur> obtenirTousLesUtilisateurs() {
        return utilisateurDAO.findAll();
    }
    
    @Override
    public List<Utilisateur> obtenirUtilisateursParType(TypeUtilisateur type) {
        if (type == null) {
            throw new IllegalArgumentException("Le type d'utilisateur ne peut pas être null");
        }
        return utilisateurDAO.findByType(type);
    }
    
    @Override
    public List<Utilisateur> obtenirUtilisateursActifs() {
        return utilisateurDAO.findActiveUsers();
    }
    
    @Override
    public boolean changerStatutUtilisateur(Long utilisateurId, boolean actif) {
        if (utilisateurId == null) {
            return false;
        }
        
        Optional<Utilisateur> utilisateurOpt = utilisateurDAO.findById(utilisateurId);
        if (!utilisateurOpt.isPresent()) {
            return false;
        }
        
        Utilisateur utilisateur = utilisateurOpt.get();
        utilisateur.setActif(actif);
        
        try {
            utilisateurDAO.update(utilisateur);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public void validerUtilisateur(Utilisateur utilisateur, String motDePasse) {
        if (utilisateur == null) {
            throw new IllegalArgumentException("L'utilisateur ne peut pas être null");
        }
        
        // Validation du nom
        if (utilisateur.getNom() == null || utilisateur.getNom().trim().isEmpty()) {
            throw new IllegalArgumentException("Le nom est requis");
        }
        if (utilisateur.getNom().trim().length() < 2 || utilisateur.getNom().trim().length() > 100) {
            throw new IllegalArgumentException("Le nom doit contenir entre 2 et 100 caractères");
        }
        
        // Validation du prénom
        if (utilisateur.getPrenom() == null || utilisateur.getPrenom().trim().isEmpty()) {
            throw new IllegalArgumentException("Le prénom est requis");
        }
        if (utilisateur.getPrenom().trim().length() < 2 || utilisateur.getPrenom().trim().length() > 100) {
            throw new IllegalArgumentException("Le prénom doit contenir entre 2 et 100 caractères");
        }
        
        // Validation de l'email
        if (utilisateur.getEmail() == null || utilisateur.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("L'email est requis");
        }
        if (!isValidEmail(utilisateur.getEmail())) {
            throw new IllegalArgumentException("Format d'email invalide");
        }
        
        // Validation du téléphone (optionnel)
        if (utilisateur.getTelephone() != null && !utilisateur.getTelephone().trim().isEmpty()) {
            if (!isValidPhone(utilisateur.getTelephone())) {
                throw new IllegalArgumentException("Format de téléphone invalide");
            }
        }
        
        // Validation du mot de passe (si fourni)
        if (motDePasse != null) {
            String messageErreur = PasswordUtil.getPasswordStrengthMessage(motDePasse);
            if (messageErreur != null) {
                throw new IllegalArgumentException(messageErreur);
            }
        }
        
        // Normaliser les données
        utilisateur.setNom(utilisateur.getNom().trim());
        utilisateur.setPrenom(utilisateur.getPrenom().trim());
        utilisateur.setEmail(utilisateur.getEmail().trim().toLowerCase());
        if (utilisateur.getTelephone() != null) {
            utilisateur.setTelephone(utilisateur.getTelephone().trim());
        }
    }
    
    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.matches(emailRegex);
    }
    
    private boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return true; // Le téléphone est optionnel
        }
        
        // Accepter les formats français courants
        String phoneRegex = "^(?:(?:\\+33|0)[1-9](?:[0-9]{8}))$";
        String cleanPhone = phone.replaceAll("[\\s.-]", "");
        return cleanPhone.matches(phoneRegex);
    }
}

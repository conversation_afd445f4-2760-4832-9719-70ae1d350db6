package com.train.service;

import com.train.model.Voyage;
import com.train.model.Trajet;
import com.train.model.StatutVoyage;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Interface de service pour la gestion des voyages
 */
public interface VoyageService {
    
    /**
     * Crée un nouveau voyage
     * @param voyage le voyage à créer
     * @return le voyage créé
     */
    Voyage creerVoyage(Voyage voyage);
    
    /**
     * Met à jour un voyage existant
     * @param voyage le voyage à mettre à jour
     * @return le voyage mis à jour
     */
    Voyage mettreAJourVoyage(Voyage voyage);
    
    /**
     * Trouve un voyage par son ID
     * @param id l'ID du voyage
     * @return Optional contenant le voyage si trouvé
     */
    Optional<Voyage> trouverParId(Long id);
    
    /**
     * Trouve un voyage par trajet et date
     * @param trajetId l'ID du trajet
     * @param dateVoyage la date du voyage
     * @return Optional contenant le voyage si trouvé
     */
    Optional<Voyage> trouverParTrajetEtDate(Long trajetId, LocalDate dateVoyage);
    
    /**
     * Recherche les voyages disponibles
     * @param villeDepart ville de départ
     * @param villeArrivee ville d'arrivée
     * @param dateVoyage date du voyage
     * @return liste des voyages disponibles
     */
    List<Voyage> rechercherVoyagesDisponibles(String villeDepart, String villeArrivee, LocalDate dateVoyage);
    
    /**
     * Obtient tous les voyages pour une date donnée
     * @param dateVoyage la date du voyage
     * @return liste des voyages
     */
    List<Voyage> obtenirVoyagesParDate(LocalDate dateVoyage);
    
    /**
     * Obtient les voyages dans une période
     * @param dateDebut date de début
     * @param dateFin date de fin
     * @return liste des voyages dans la période
     */
    List<Voyage> obtenirVoyagesParPeriode(LocalDate dateDebut, LocalDate dateFin);
    
    /**
     * Obtient les voyages par statut
     * @param statut le statut du voyage
     * @return liste des voyages avec ce statut
     */
    List<Voyage> obtenirVoyagesParStatut(StatutVoyage statut);
    
    /**
     * Réserve des places dans un voyage
     * @param voyageId l'ID du voyage
     * @param nombrePlaces nombre de places à réserver
     * @return true si la réservation a réussi
     * @throws IllegalStateException si pas assez de places disponibles
     */
    boolean reserverPlaces(Long voyageId, int nombrePlaces);
    
    /**
     * Libère des places dans un voyage
     * @param voyageId l'ID du voyage
     * @param nombrePlaces nombre de places à libérer
     * @return true si la libération a réussi
     */
    boolean libererPlaces(Long voyageId, int nombrePlaces);
    
    /**
     * Change le statut d'un voyage
     * @param voyageId l'ID du voyage
     * @param nouveauStatut le nouveau statut
     * @return true si le changement a réussi
     */
    boolean changerStatutVoyage(Long voyageId, StatutVoyage nouveauStatut);
    
    /**
     * Génère automatiquement les voyages pour un trajet sur une période
     * @param trajetId l'ID du trajet
     * @param dateDebut date de début
     * @param dateFin date de fin
     * @return nombre de voyages créés
     */
    int genererVoyagesPourTrajet(Long trajetId, LocalDate dateDebut, LocalDate dateFin);
    
    /**
     * Vérifie si un voyage a des places disponibles
     * @param voyageId l'ID du voyage
     * @param nombrePlaces nombre de places demandées
     * @return true si assez de places disponibles
     */
    boolean aPlacesDisponibles(Long voyageId, int nombrePlaces);
    
    /**
     * Obtient le taux d'occupation d'un voyage
     * @param voyageId l'ID du voyage
     * @return taux d'occupation en pourcentage
     */
    double obtenirTauxOccupation(Long voyageId);
    
    /**
     * Supprime un voyage (si aucune réservation)
     * @param voyageId l'ID du voyage
     * @return true si la suppression a réussi
     */
    boolean supprimerVoyage(Long voyageId);
    
    /**
     * Valide les données d'un voyage
     * @param voyage le voyage à valider
     * @throws IllegalArgumentException si les données sont invalides
     */
    void validerVoyage(Voyage voyage);
}

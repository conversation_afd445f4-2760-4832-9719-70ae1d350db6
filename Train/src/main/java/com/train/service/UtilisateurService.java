package com.train.service;

import com.train.model.Utilisateur;
import com.train.model.TypeUtilisateur;
import java.util.List;
import java.util.Optional;

/**
 * Interface de service pour la gestion des utilisateurs
 */
public interface UtilisateurService {
    
    /**
     * Crée un nouveau compte utilisateur
     * @param utilisateur les données de l'utilisateur
     * @param motDePasseClair le mot de passe en clair
     * @return l'utilisateur créé
     * @throws IllegalArgumentException si les données sont invalides
     */
    Utilisateur creerCompte(Utilisateur utilisateur, String motDePasseClair);
    
    /**
     * Authentifie un utilisateur
     * @param email l'email de l'utilisateur
     * @param motDePasse le mot de passe en clair
     * @return Optional contenant l'utilisateur si l'authentification réussit
     */
    Optional<Utilisateur> authentifier(String email, String motDePasse);
    
    /**
     * Met à jour le profil d'un utilisateur
     * @param utilisateur l'utilisateur à mettre à jour
     * @return l'utilisateur mis à jour
     */
    Utilisateur mettreAJourProfil(Utilisateur utilisateur);
    
    /**
     * Change le mot de passe d'un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     * @param ancienMotDePasse l'ancien mot de passe
     * @param nouveauMotDePasse le nouveau mot de passe
     * @return true si le changement a réussi
     */
    boolean changerMotDePasse(Long utilisateurId, String ancienMotDePasse, String nouveauMotDePasse);
    
    /**
     * Trouve un utilisateur par son ID
     * @param id l'ID de l'utilisateur
     * @return Optional contenant l'utilisateur si trouvé
     */
    Optional<Utilisateur> trouverParId(Long id);
    
    /**
     * Trouve un utilisateur par son email
     * @param email l'email de l'utilisateur
     * @return Optional contenant l'utilisateur si trouvé
     */
    Optional<Utilisateur> trouverParEmail(String email);
    
    /**
     * Vérifie si un email est déjà utilisé
     * @param email l'email à vérifier
     * @return true si l'email existe déjà
     */
    boolean emailExiste(String email);
    
    /**
     * Récupère tous les utilisateurs
     * @return liste de tous les utilisateurs
     */
    List<Utilisateur> obtenirTousLesUtilisateurs();
    
    /**
     * Récupère les utilisateurs par type
     * @param type le type d'utilisateur
     * @return liste des utilisateurs du type spécifié
     */
    List<Utilisateur> obtenirUtilisateursParType(TypeUtilisateur type);
    
    /**
     * Récupère les utilisateurs actifs
     * @return liste des utilisateurs actifs
     */
    List<Utilisateur> obtenirUtilisateursActifs();
    
    /**
     * Active ou désactive un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     * @param actif true pour activer, false pour désactiver
     * @return true si la mise à jour a réussi
     */
    boolean changerStatutUtilisateur(Long utilisateurId, boolean actif);
    
    /**
     * Valide les données d'un utilisateur
     * @param utilisateur l'utilisateur à valider
     * @param motDePasse le mot de passe (optionnel)
     * @throws IllegalArgumentException si les données sont invalides
     */
    void validerUtilisateur(Utilisateur utilisateur, String motDePasse);
}

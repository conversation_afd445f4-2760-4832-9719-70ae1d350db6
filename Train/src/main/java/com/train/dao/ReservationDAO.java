package com.train.dao;

import com.train.model.Reservation;
import com.train.model.Utilisateur;
import com.train.model.Voyage;
import com.train.model.StatutReservation;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Interface DAO pour les opérations sur les réservations
 */
public interface ReservationDAO extends BaseDAO<Reservation, Long> {
    
    /**
     * Trouve une réservation par son numéro
     * @param numeroReservation le numéro de réservation
     * @return Optional contenant la réservation si trouvée
     */
    Optional<Reservation> findByNumero(String numeroReservation);
    
    /**
     * Trouve les réservations d'un utilisateur
     * @param utilisateur l'utilisateur
     * @return liste des réservations de l'utilisateur
     */
    List<Reservation> findByUtilisateur(Utilisateur utilisateur);
    
    /**
     * Trouve les réservations d'un utilisateur par ID
     * @param utilisateurId l'ID de l'utilisateur
     * @return liste des réservations de l'utilisateur
     */
    List<Reservation> findByUtilisateurId(Long utilisateurId);
    
    /**
     * Trouve les réservations pour un voyage
     * @param voyage le voyage
     * @return liste des réservations pour ce voyage
     */
    List<Reservation> findByVoyage(Voyage voyage);
    
    /**
     * Trouve les réservations par statut
     * @param statut le statut de réservation
     * @return liste des réservations avec ce statut
     */
    List<Reservation> findByStatut(StatutReservation statut);
    
    /**
     * Trouve les réservations actives d'un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     * @return liste des réservations actives
     */
    List<Reservation> findActiveReservationsByUser(Long utilisateurId);
    
    /**
     * Trouve les réservations par date de voyage
     * @param dateVoyage la date du voyage
     * @return liste des réservations pour cette date
     */
    List<Reservation> findByDateVoyage(LocalDate dateVoyage);
    
    /**
     * Trouve les réservations entre deux dates
     * @param dateDebut date de début
     * @param dateFin date de fin
     * @return liste des réservations dans cette période
     */
    List<Reservation> findByDateRange(LocalDate dateDebut, LocalDate dateFin);
    
    /**
     * Compte le nombre de réservations pour un voyage
     * @param voyageId l'ID du voyage
     * @return nombre de réservations
     */
    long countByVoyage(Long voyageId);
    
    /**
     * Calcule le total des places réservées pour un voyage
     * @param voyageId l'ID du voyage
     * @return nombre total de places réservées
     */
    int getTotalPlacesReservees(Long voyageId);
    
    /**
     * Vérifie si un utilisateur a déjà une réservation pour un voyage
     * @param utilisateurId l'ID de l'utilisateur
     * @param voyageId l'ID du voyage
     * @return true si une réservation existe
     */
    boolean hasReservationForVoyage(Long utilisateurId, Long voyageId);
}

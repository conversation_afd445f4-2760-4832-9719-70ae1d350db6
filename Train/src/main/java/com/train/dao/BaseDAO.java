package com.train.dao;

import java.util.List;
import java.util.Optional;

/**
 * Interface générique pour les opérations CRUD de base
 * @param <T> Type de l'entité
 * @param <ID> Type de l'identifiant
 */
public interface BaseDAO<T, ID> {
    
    /**
     * Sauvegarde une entité
     * @param entity l'entité à sauvegarder
     * @return l'entité sauvegardée avec son ID généré
     */
    T save(T entity);
    
    /**
     * Met à jour une entité existante
     * @param entity l'entité à mettre à jour
     * @return l'entité mise à jour
     */
    T update(T entity);
    
    /**
     * Supprime une entité par son ID
     * @param id l'identifiant de l'entité à supprimer
     * @return true si la suppression a réussi, false sinon
     */
    boolean deleteById(ID id);
    
    /**
     * Trouve une entité par son ID
     * @param id l'identifiant de l'entité
     * @return Optional contenant l'entité si trouvée, Optional.empty() sinon
     */
    Optional<T> findById(ID id);
    
    /**
     * R<PERSON>cup<PERSON> toutes les entités
     * @return liste de toutes les entités
     */
    List<T> findAll();
    
    /**
     * Vérifie si une entité existe par son ID
     * @param id l'identifiant de l'entité
     * @return true si l'entité existe, false sinon
     */
    boolean existsById(ID id);
    
    /**
     * Compte le nombre total d'entités
     * @return le nombre d'entités
     */
    long count();
}

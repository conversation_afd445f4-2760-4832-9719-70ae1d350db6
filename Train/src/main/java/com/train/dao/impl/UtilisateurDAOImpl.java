package com.train.dao.impl;

import com.train.dao.UtilisateurDAO;
import com.train.model.Utilisateur;
import com.train.model.TypeUtilisateur;
import com.train.util.DatabaseConnection;
import com.train.util.PasswordUtil;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation DAO pour les utilisateurs
 */
public class UtilisateurDAOImpl implements UtilisateurDAO {
    
    private static final String INSERT_USER = 
        "INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, telephone, type_utilisateur, date_creation, actif) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    private static final String UPDATE_USER = 
        "UPDATE utilisateurs SET nom=?, prenom=?, email=?, telephone=?, type_utilisateur=?, actif=? WHERE id=?";
    
    private static final String DELETE_USER = "DELETE FROM utilisateurs WHERE id=?";
    
    private static final String FIND_BY_ID = "SELECT * FROM utilisateurs WHERE id=?";
    
    private static final String FIND_ALL = "SELECT * FROM utilisateurs ORDER BY nom, prenom";
    
    private static final String FIND_BY_EMAIL = "SELECT * FROM utilisateurs WHERE email=?";
    
    private static final String EXISTS_BY_EMAIL = "SELECT COUNT(*) FROM utilisateurs WHERE email=?";
    
    private static final String FIND_BY_TYPE = "SELECT * FROM utilisateurs WHERE type_utilisateur=? ORDER BY nom, prenom";
    
    private static final String FIND_ACTIVE = "SELECT * FROM utilisateurs WHERE actif=true ORDER BY nom, prenom";
    
    private static final String UPDATE_LAST_LOGIN = "UPDATE utilisateurs SET derniere_connexion=? WHERE id=?";
    
    private static final String CHANGE_PASSWORD = "UPDATE utilisateurs SET mot_de_passe=? WHERE id=?";
    
    private static final String COUNT_USERS = "SELECT COUNT(*) FROM utilisateurs";
    
    @Override
    public Utilisateur save(Utilisateur utilisateur) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(INSERT_USER, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setString(1, utilisateur.getNom());
            stmt.setString(2, utilisateur.getPrenom());
            stmt.setString(3, utilisateur.getEmail());
            stmt.setString(4, utilisateur.getMotDePasse());
            stmt.setString(5, utilisateur.getTelephone());
            stmt.setString(6, utilisateur.getTypeUtilisateur().name());
            stmt.setTimestamp(7, Timestamp.valueOf(utilisateur.getDateCreation()));
            stmt.setBoolean(8, utilisateur.isActif());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Échec de la création de l'utilisateur");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    utilisateur.setId(generatedKeys.getLong(1));
                }
            }
            
            return utilisateur;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la sauvegarde de l'utilisateur", e);
        }
    }
    
    @Override
    public Utilisateur update(Utilisateur utilisateur) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_USER)) {
            
            stmt.setString(1, utilisateur.getNom());
            stmt.setString(2, utilisateur.getPrenom());
            stmt.setString(3, utilisateur.getEmail());
            stmt.setString(4, utilisateur.getTelephone());
            stmt.setString(5, utilisateur.getTypeUtilisateur().name());
            stmt.setBoolean(6, utilisateur.isActif());
            stmt.setLong(7, utilisateur.getId());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Utilisateur non trouvé pour la mise à jour");
            }
            
            return utilisateur;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la mise à jour de l'utilisateur", e);
        }
    }
    
    @Override
    public boolean deleteById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(DELETE_USER)) {
            
            stmt.setLong(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la suppression de l'utilisateur", e);
        }
    }
    
    @Override
    public Optional<Utilisateur> findById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_ID)) {
            
            stmt.setLong(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToUtilisateur(rs));
                }
            }
            return Optional.empty();
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche de l'utilisateur", e);
        }
    }
    
    @Override
    public List<Utilisateur> findAll() {
        List<Utilisateur> utilisateurs = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ALL);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                utilisateurs.add(mapResultSetToUtilisateur(rs));
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des utilisateurs", e);
        }
        return utilisateurs;
    }
    
    @Override
    public Optional<Utilisateur> findByEmail(String email) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_EMAIL)) {
            
            stmt.setString(1, email);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToUtilisateur(rs));
                }
            }
            return Optional.empty();
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par email", e);
        }
    }
    
    @Override
    public boolean existsByEmail(String email) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(EXISTS_BY_EMAIL)) {
            
            stmt.setString(1, email);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
            return false;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la vérification de l'email", e);
        }
    }
    
    @Override
    public List<Utilisateur> findByType(TypeUtilisateur type) {
        List<Utilisateur> utilisateurs = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_TYPE)) {
            
            stmt.setString(1, type.name());
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    utilisateurs.add(mapResultSetToUtilisateur(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par type", e);
        }
        return utilisateurs;
    }
    
    @Override
    public List<Utilisateur> findActiveUsers() {
        List<Utilisateur> utilisateurs = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ACTIVE);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                utilisateurs.add(mapResultSetToUtilisateur(rs));
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche des utilisateurs actifs", e);
        }
        return utilisateurs;
    }
    
    @Override
    public Optional<Utilisateur> authenticate(String email, String motDePasse) {
        Optional<Utilisateur> userOpt = findByEmail(email);
        if (userOpt.isPresent()) {
            Utilisateur user = userOpt.get();
            if (user.isActif() && PasswordUtil.verifyPassword(motDePasse, user.getMotDePasse())) {
                updateLastLogin(user.getId());
                return userOpt;
            }
        }
        return Optional.empty();
    }
    
    @Override
    public void updateLastLogin(Long userId) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_LAST_LOGIN)) {
            
            stmt.setTimestamp(1, Timestamp.valueOf(LocalDateTime.now()));
            stmt.setLong(2, userId);
            stmt.executeUpdate();
        } catch (SQLException e) {
            // Log l'erreur mais ne pas faire échouer l'authentification
            System.err.println("Erreur lors de la mise à jour de la dernière connexion: " + e.getMessage());
        }
    }
    
    @Override
    public boolean changePassword(Long userId, String nouveauMotDePasse) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(CHANGE_PASSWORD)) {
            
            stmt.setString(1, nouveauMotDePasse);
            stmt.setLong(2, userId);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors du changement de mot de passe", e);
        }
    }
    
    @Override
    public boolean existsById(Long id) {
        return findById(id).isPresent();
    }
    
    @Override
    public long count() {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(COUNT_USERS);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors du comptage des utilisateurs", e);
        }
    }
    
    private Utilisateur mapResultSetToUtilisateur(ResultSet rs) throws SQLException {
        Utilisateur utilisateur = new Utilisateur();
        utilisateur.setId(rs.getLong("id"));
        utilisateur.setNom(rs.getString("nom"));
        utilisateur.setPrenom(rs.getString("prenom"));
        utilisateur.setEmail(rs.getString("email"));
        utilisateur.setMotDePasse(rs.getString("mot_de_passe"));
        utilisateur.setTelephone(rs.getString("telephone"));
        utilisateur.setTypeUtilisateur(TypeUtilisateur.valueOf(rs.getString("type_utilisateur")));
        
        Timestamp dateCreation = rs.getTimestamp("date_creation");
        if (dateCreation != null) {
            utilisateur.setDateCreation(dateCreation.toLocalDateTime());
        }
        
        Timestamp derniereConnexion = rs.getTimestamp("derniere_connexion");
        if (derniereConnexion != null) {
            utilisateur.setDerniereConnexion(derniereConnexion.toLocalDateTime());
        }
        
        utilisateur.setActif(rs.getBoolean("actif"));
        return utilisateur;
    }
}

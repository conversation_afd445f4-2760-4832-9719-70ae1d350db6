package com.train.dao.impl;

import com.train.dao.VoyageDAO;
import com.train.dao.TrajetDAO;
import com.train.dao.impl.TrajetDAOImpl;
import com.train.model.*;
import com.train.util.DatabaseConnection;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation DAO pour les voyages
 */
public class VoyageDAOImpl implements VoyageDAO {
    
    private final TrajetDAO trajetDAO = new TrajetDAOImpl();
    
    private static final String INSERT_VOYAGE = 
        "INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut, date_creation) " +
        "VALUES (?, ?, ?, ?, ?, ?)";
    
    private static final String UPDATE_VOYAGE = 
        "UPDATE voyages SET trajet_id=?, date_voyage=?, places_disponibles=?, places_reservees=?, statut=? WHERE id=?";
    
    private static final String DELETE_VOYAGE = "DELETE FROM voyages WHERE id=?";
    
    private static final String FIND_BY_ID = 
        "SELECT v.*, t.gare_depart_id, t.gare_arrivee_id, t.heure_depart, t.heure_arrivee, t.prix, t.nombre_places, t.actif, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM voyages v " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE v.id=?";
    
    private static final String FIND_ALL = 
        "SELECT v.*, t.gare_depart_id, t.gare_arrivee_id, t.heure_depart, t.heure_arrivee, t.prix, t.nombre_places, t.actif, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM voyages v " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "ORDER BY v.date_voyage, t.heure_depart";
    
    private static final String FIND_BY_TRAJET_AND_DATE = 
        "SELECT v.*, t.gare_depart_id, t.gare_arrivee_id, t.heure_depart, t.heure_arrivee, t.prix, t.nombre_places, t.actif, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM voyages v " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE v.trajet_id=? AND v.date_voyage=?";
    
    private static final String FIND_BY_DATE = 
        "SELECT v.*, t.gare_depart_id, t.gare_arrivee_id, t.heure_depart, t.heure_arrivee, t.prix, t.nombre_places, t.actif, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM voyages v " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE v.date_voyage=? " +
        "ORDER BY t.heure_depart";
    
    private static final String FIND_BY_DATE_RANGE = 
        "SELECT v.*, t.gare_depart_id, t.gare_arrivee_id, t.heure_depart, t.heure_arrivee, t.prix, t.nombre_places, t.actif, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM voyages v " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE v.date_voyage BETWEEN ? AND ? " +
        "ORDER BY v.date_voyage, t.heure_depart";
    
    private static final String FIND_BY_STATUT = 
        "SELECT v.*, t.gare_depart_id, t.gare_arrivee_id, t.heure_depart, t.heure_arrivee, t.prix, t.nombre_places, t.actif, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM voyages v " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE v.statut=? " +
        "ORDER BY v.date_voyage, t.heure_depart";
    
    private static final String FIND_AVAILABLE = 
        "SELECT v.*, t.gare_depart_id, t.gare_arrivee_id, t.heure_depart, t.heure_arrivee, t.prix, t.nombre_places, t.actif, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM voyages v " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE v.date_voyage=? AND v.places_disponibles > 0 AND v.statut='PROGRAMME' AND t.actif=true " +
        "ORDER BY t.heure_depart";
    
    private static final String SEARCH_AVAILABLE = 
        "SELECT v.*, t.gare_depart_id, t.gare_arrivee_id, t.heure_depart, t.heure_arrivee, t.prix, t.nombre_places, t.actif, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM voyages v " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE gd.ville LIKE ? AND ga.ville LIKE ? AND v.date_voyage=? " +
        "AND v.places_disponibles > 0 AND v.statut='PROGRAMME' AND t.actif=true " +
        "ORDER BY t.heure_depart";
    
    private static final String UPDATE_PLACES = 
        "UPDATE voyages SET places_disponibles = places_disponibles - ?, places_reservees = places_reservees + ? WHERE id=? AND places_disponibles >= ?";
    
    private static final String LIBERE_PLACES = 
        "UPDATE voyages SET places_disponibles = places_disponibles + ?, places_reservees = places_reservees - ? WHERE id=?";
    
    private static final String COUNT_VOYAGES = "SELECT COUNT(*) FROM voyages";
    
    @Override
    public Voyage save(Voyage voyage) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(INSERT_VOYAGE, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setLong(1, voyage.getTrajet().getId());
            stmt.setDate(2, Date.valueOf(voyage.getDateVoyage()));
            stmt.setInt(3, voyage.getPlacesDisponibles());
            stmt.setInt(4, voyage.getPlacesReservees());
            stmt.setString(5, voyage.getStatut().name());
            stmt.setTimestamp(6, Timestamp.valueOf(voyage.getDateCreation()));
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Échec de la création du voyage");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    voyage.setId(generatedKeys.getLong(1));
                }
            }
            
            return voyage;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la sauvegarde du voyage", e);
        }
    }
    
    @Override
    public Voyage update(Voyage voyage) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_VOYAGE)) {
            
            stmt.setLong(1, voyage.getTrajet().getId());
            stmt.setDate(2, Date.valueOf(voyage.getDateVoyage()));
            stmt.setInt(3, voyage.getPlacesDisponibles());
            stmt.setInt(4, voyage.getPlacesReservees());
            stmt.setString(5, voyage.getStatut().name());
            stmt.setLong(6, voyage.getId());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Voyage non trouvé pour la mise à jour");
            }
            
            return voyage;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la mise à jour du voyage", e);
        }
    }
    
    @Override
    public boolean deleteById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(DELETE_VOYAGE)) {
            
            stmt.setLong(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la suppression du voyage", e);
        }
    }
    
    @Override
    public Optional<Voyage> findById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_ID)) {
            
            stmt.setLong(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToVoyage(rs));
                }
            }
            return Optional.empty();
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche du voyage", e);
        }
    }
    
    @Override
    public List<Voyage> findAll() {
        List<Voyage> voyages = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ALL);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                voyages.add(mapResultSetToVoyage(rs));
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des voyages", e);
        }
        return voyages;
    }
    
    @Override
    public Optional<Voyage> findByTrajetAndDate(Trajet trajet, LocalDate dateVoyage) {
        return findByTrajetIdAndDate(trajet.getId(), dateVoyage);
    }
    
    @Override
    public Optional<Voyage> findByTrajetIdAndDate(Long trajetId, LocalDate dateVoyage) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_TRAJET_AND_DATE)) {
            
            stmt.setLong(1, trajetId);
            stmt.setDate(2, Date.valueOf(dateVoyage));
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToVoyage(rs));
                }
            }
            return Optional.empty();
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par trajet et date", e);
        }
    }
    
    @Override
    public List<Voyage> findByTrajet(Trajet trajet) {
        List<Voyage> voyages = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(
                 "SELECT v.*, t.gare_depart_id, t.gare_arrivee_id, t.heure_depart, t.heure_arrivee, t.prix, t.nombre_places, t.actif, " +
                 "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
                 "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
                 "FROM voyages v " +
                 "JOIN trajets t ON v.trajet_id = t.id " +
                 "JOIN gares gd ON t.gare_depart_id = gd.id " +
                 "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
                 "WHERE v.trajet_id=? ORDER BY v.date_voyage")) {
            
            stmt.setLong(1, trajet.getId());
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    voyages.add(mapResultSetToVoyage(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par trajet", e);
        }
        return voyages;
    }
    
    @Override
    public List<Voyage> findByDate(LocalDate dateVoyage) {
        List<Voyage> voyages = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_DATE)) {
            
            stmt.setDate(1, Date.valueOf(dateVoyage));
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    voyages.add(mapResultSetToVoyage(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par date", e);
        }
        return voyages;
    }
    
    @Override
    public List<Voyage> findByDateRange(LocalDate dateDebut, LocalDate dateFin) {
        List<Voyage> voyages = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_DATE_RANGE)) {
            
            stmt.setDate(1, Date.valueOf(dateDebut));
            stmt.setDate(2, Date.valueOf(dateFin));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    voyages.add(mapResultSetToVoyage(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par période", e);
        }
        return voyages;
    }
    
    @Override
    public List<Voyage> findByStatut(StatutVoyage statut) {
        List<Voyage> voyages = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_STATUT)) {
            
            stmt.setString(1, statut.name());
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    voyages.add(mapResultSetToVoyage(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par statut", e);
        }
        return voyages;
    }
    
    @Override
    public List<Voyage> findAvailableVoyages(LocalDate dateVoyage) {
        List<Voyage> voyages = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_AVAILABLE)) {
            
            stmt.setDate(1, Date.valueOf(dateVoyage));
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    voyages.add(mapResultSetToVoyage(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche des voyages disponibles", e);
        }
        return voyages;
    }
    
    @Override
    public List<Voyage> searchAvailableVoyages(String villeDepart, String villeArrivee, LocalDate dateVoyage) {
        List<Voyage> voyages = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SEARCH_AVAILABLE)) {
            
            stmt.setString(1, "%" + villeDepart + "%");
            stmt.setString(2, "%" + villeArrivee + "%");
            stmt.setDate(3, Date.valueOf(dateVoyage));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    voyages.add(mapResultSetToVoyage(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche de voyages", e);
        }
        return voyages;
    }
    
    @Override
    public boolean updatePlaces(Long voyageId, int placesReservees) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_PLACES)) {
            
            stmt.setInt(1, placesReservees);
            stmt.setInt(2, placesReservees);
            stmt.setLong(3, voyageId);
            stmt.setInt(4, placesReservees);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la mise à jour des places", e);
        }
    }
    
    @Override
    public boolean liberePlaces(Long voyageId, int placesALiberer) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(LIBERE_PLACES)) {
            
            stmt.setInt(1, placesALiberer);
            stmt.setInt(2, placesALiberer);
            stmt.setLong(3, voyageId);
            
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la libération des places", e);
        }
    }
    
    @Override
    public boolean existsById(Long id) {
        return findById(id).isPresent();
    }
    
    @Override
    public long count() {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(COUNT_VOYAGES);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors du comptage des voyages", e);
        }
    }
    
    private Voyage mapResultSetToVoyage(ResultSet rs) throws SQLException {
        Voyage voyage = new Voyage();
        voyage.setId(rs.getLong("id"));
        voyage.setDateVoyage(rs.getDate("date_voyage").toLocalDate());
        voyage.setPlacesDisponibles(rs.getInt("places_disponibles"));
        voyage.setPlacesReservees(rs.getInt("places_reservees"));
        voyage.setStatut(StatutVoyage.valueOf(rs.getString("statut")));
        
        Timestamp dateCreation = rs.getTimestamp("date_creation");
        if (dateCreation != null) {
            voyage.setDateCreation(dateCreation.toLocalDateTime());
        }
        
        // Créer le trajet associé
        Trajet trajet = new Trajet();
        trajet.setId(rs.getLong("trajet_id"));
        trajet.setHeureDepart(rs.getTime("heure_depart").toLocalTime());
        trajet.setHeureArrivee(rs.getTime("heure_arrivee").toLocalTime());
        trajet.setPrix(rs.getBigDecimal("prix"));
        trajet.setNombrePlaces(rs.getInt("nombre_places"));
        trajet.setActif(rs.getBoolean("actif"));
        
        // Gare de départ
        Gare gareDepart = new Gare();
        gareDepart.setId(rs.getLong("gare_depart_id"));
        gareDepart.setNom(rs.getString("gare_depart_nom"));
        gareDepart.setVille(rs.getString("gare_depart_ville"));
        gareDepart.setCodeGare(rs.getString("gare_depart_code"));
        trajet.setGareDepart(gareDepart);
        
        // Gare d'arrivée
        Gare gareArrivee = new Gare();
        gareArrivee.setId(rs.getLong("gare_arrivee_id"));
        gareArrivee.setNom(rs.getString("gare_arrivee_nom"));
        gareArrivee.setVille(rs.getString("gare_arrivee_ville"));
        gareArrivee.setCodeGare(rs.getString("gare_arrivee_code"));
        trajet.setGareArrivee(gareArrivee);
        
        voyage.setTrajet(trajet);
        
        return voyage;
    }
}

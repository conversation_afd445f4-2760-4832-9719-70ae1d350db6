package com.train.server;

import com.train.servlet.*;
import com.train.util.DemoDataManager;

import javax.servlet.http.HttpServlet;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.file.Files;
import java.nio.file.Paths;
import com.sun.net.httpserver.HttpServer;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpExchange;
import java.io.OutputStream;
import java.io.InputStream;

/**
 * Serveur HTTP simple pour l'application TrainSystem
 */
public class SimpleServer {
    
    private static final int PORT = 8080;
    private static HttpServer server;
    
    public static void main(String[] args) {
        try {
            // Initialiser les données de démonstration
            System.out.println("🔄 Initialisation des données de démonstration...");
            DemoDataManager.reset();
            
            // Créer le serveur HTTP
            server = HttpServer.create(new InetSocketAddress(PORT), 0);
            
            // Configurer les routes
            setupRoutes();
            
            // Démarrer le serveur
            server.setExecutor(null);
            server.start();
            
            System.out.println("🚀 Serveur TrainSystem démarré avec succès !");
            System.out.println("📍 URL: http://localhost:" + PORT + "/Train");
            System.out.println("🔑 Compte test: <EMAIL> / password");
            System.out.println("⚠️  Pour arrêter le serveur, appuyez sur Ctrl+C");
            System.out.println();
            
            // Ouvrir le navigateur
            try {
                String os = System.getProperty("os.name").toLowerCase();
                if (os.contains("win")) {
                    Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler http://localhost:" + PORT + "/Train");
                }
            } catch (Exception e) {
                System.out.println("Impossible d'ouvrir le navigateur automatiquement");
            }
            
            // Garder le serveur en vie
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                System.out.println("\n🛑 Arrêt du serveur...");
                server.stop(0);
                System.out.println("✅ Serveur arrêté");
            }));
            
            // Attendre indéfiniment
            Thread.currentThread().join();
            
        } catch (Exception e) {
            System.err.println("❌ Erreur lors du démarrage du serveur: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void setupRoutes() {
        // Page d'accueil
        server.createContext("/Train", new HomeHandler());
        server.createContext("/Train/", new HomeHandler());
        
        // Pages statiques
        server.createContext("/Train/login", new StaticPageHandler("login"));
        server.createContext("/Train/register", new StaticPageHandler("register"));
        server.createContext("/Train/search", new StaticPageHandler("search"));
        
        // API de test
        server.createContext("/Train/api/test", new TestHandler());
        
        System.out.println("✅ Routes configurées");
    }
    
    static class HomeHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = generateHomePage();
            exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
            exchange.sendResponseHeaders(200, response.getBytes("UTF-8").length);
            OutputStream os = exchange.getResponseBody();
            os.write(response.getBytes("UTF-8"));
            os.close();
        }
    }
    
    static class StaticPageHandler implements HttpHandler {
        private final String page;
        
        public StaticPageHandler(String page) {
            this.page = page;
        }
        
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = generatePage(page);
            exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
            exchange.sendResponseHeaders(200, response.getBytes("UTF-8").length);
            OutputStream os = exchange.getResponseBody();
            os.write(response.getBytes("UTF-8"));
            os.close();
        }
    }
    
    static class TestHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = "{ \"status\": \"OK\", \"message\": \"TrainSystem API fonctionne!\", \"data\": " + 
                            DemoDataManager.getAllGares().size() + " gares disponibles\" }";
            exchange.getResponseHeaders().set("Content-Type", "application/json");
            exchange.sendResponseHeaders(200, response.length());
            OutputStream os = exchange.getResponseBody();
            os.write(response.getBytes());
            os.close();
        }
    }
    
    private static String generateHomePage() {
        return "<!DOCTYPE html>" +
               "<html lang='fr'>" +
               "<head>" +
               "<meta charset='UTF-8'>" +
               "<meta name='viewport' content='width=device-width, initial-scale=1.0'>" +
               "<title>TrainSystem - Accueil</title>" +
               "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>" +
               "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>" +
               "</head>" +
               "<body>" +
               "<nav class='navbar navbar-expand-lg navbar-dark bg-dark'>" +
               "<div class='container'>" +
               "<a class='navbar-brand' href='/Train'><i class='fas fa-train'></i> TrainSystem</a>" +
               "</div>" +
               "</nav>" +
               "<div class='container my-5'>" +
               "<div class='jumbotron bg-primary text-white p-5 rounded'>" +
               "<h1 class='display-4'>🚂 Bienvenue sur TrainSystem</h1>" +
               "<p class='lead'>Votre plateforme de réservation de billets de train en ligne</p>" +
               "<div class='alert alert-success'>" +
               "<i class='fas fa-check-circle'></i> <strong>Application déployée avec succès !</strong>" +
               "</div>" +
               "</div>" +
               "<div class='row mt-4'>" +
               "<div class='col-md-4'>" +
               "<div class='card h-100'>" +
               "<div class='card-body text-center'>" +
               "<i class='fas fa-user-plus fa-3x text-success mb-3'></i>" +
               "<h5>Inscription</h5>" +
               "<p>Créez votre compte pour commencer</p>" +
               "<a href='/Train/register' class='btn btn-success'>S'inscrire</a>" +
               "</div></div></div>" +
               "<div class='col-md-4'>" +
               "<div class='card h-100'>" +
               "<div class='card-body text-center'>" +
               "<i class='fas fa-sign-in-alt fa-3x text-primary mb-3'></i>" +
               "<h5>Connexion</h5>" +
               "<p>Connectez-vous à votre compte</p>" +
               "<a href='/Train/login' class='btn btn-primary'>Se connecter</a>" +
               "</div></div></div>" +
               "<div class='col-md-4'>" +
               "<div class='card h-100'>" +
               "<div class='card-body text-center'>" +
               "<i class='fas fa-search fa-3x text-info mb-3'></i>" +
               "<h5>Recherche</h5>" +
               "<p>Trouvez votre voyage idéal</p>" +
               "<a href='/Train/search' class='btn btn-info'>Rechercher</a>" +
               "</div></div></div>" +
               "</div>" +
               "<div class='card mt-4'>" +
               "<div class='card-header'><h5>🧪 Test de l'Application</h5></div>" +
               "<div class='card-body'>" +
               "<p><strong>Compte administrateur par défaut :</strong></p>" +
               "<ul><li>Email : <EMAIL></li><li>Mot de passe : password</li></ul>" +
               "<p><strong>Données de démonstration :</strong></p>" +
               "<ul><li>" + DemoDataManager.getAllGares().size() + " gares disponibles</li>" +
               "<li>" + DemoDataManager.getAllTrajets().size() + " trajets configurés</li>" +
               "<li>" + DemoDataManager.getAllVoyages().size() + " voyages programmés</li></ul>" +
               "<a href='/Train/api/test' class='btn btn-outline-primary' target='_blank'>Tester l'API</a>" +
               "</div></div>" +
               "</div>" +
               "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>" +
               "</body></html>";
    }
    
    private static String generatePage(String page) {
        switch (page) {
            case "login":
                return generateLoginPage();
            case "register":
                return generateRegisterPage();
            case "search":
                return generateSearchPage();
            default:
                return generateHomePage();
        }
    }
    
    private static String generateLoginPage() {
        return "<!DOCTYPE html><html><head><title>Connexion - TrainSystem</title>" +
               "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'></head>" +
               "<body><div class='container my-5'><div class='row justify-content-center'><div class='col-md-6'>" +
               "<div class='card'><div class='card-header'><h3>Connexion</h3></div><div class='card-body'>" +
               "<div class='alert alert-info'>Utilisez : <EMAIL> / password</div>" +
               "<form><div class='mb-3'><label class='form-label'>Email</label>" +
               "<input type='email' class='form-control' value='<EMAIL>'></div>" +
               "<div class='mb-3'><label class='form-label'>Mot de passe</label>" +
               "<input type='password' class='form-control' value='password'></div>" +
               "<button type='button' class='btn btn-primary' onclick='alert(\"Fonctionnalité disponible en mode complet\")'>Se connecter</button>" +
               "</form><hr><a href='/Train' class='btn btn-outline-secondary'>Retour</a>" +
               "</div></div></div></div></div></body></html>";
    }
    
    private static String generateRegisterPage() {
        return "<!DOCTYPE html><html><head><title>Inscription - TrainSystem</title>" +
               "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'></head>" +
               "<body><div class='container my-5'><div class='row justify-content-center'><div class='col-md-6'>" +
               "<div class='card'><div class='card-header'><h3>Inscription</h3></div><div class='card-body'>" +
               "<div class='alert alert-info'>Mode démonstration - Inscription simulée</div>" +
               "<form><div class='row'><div class='col-md-6 mb-3'><label class='form-label'>Prénom</label>" +
               "<input type='text' class='form-control'></div><div class='col-md-6 mb-3'><label class='form-label'>Nom</label>" +
               "<input type='text' class='form-control'></div></div>" +
               "<div class='mb-3'><label class='form-label'>Email</label><input type='email' class='form-control'></div>" +
               "<div class='mb-3'><label class='form-label'>Mot de passe</label><input type='password' class='form-control'></div>" +
               "<button type='button' class='btn btn-success' onclick='alert(\"Compte créé avec succès en mode démo!\")'>Créer le compte</button>" +
               "</form><hr><a href='/Train' class='btn btn-outline-secondary'>Retour</a>" +
               "</div></div></div></div></div></body></html>";
    }
    
    private static String generateSearchPage() {
        return "<!DOCTYPE html><html><head><title>Recherche - TrainSystem</title>" +
               "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'></head>" +
               "<body><div class='container my-5'><div class='row justify-content-center'><div class='col-md-8'>" +
               "<div class='card'><div class='card-header'><h3>Rechercher un Voyage</h3></div><div class='card-body'>" +
               "<form><div class='row'><div class='col-md-6 mb-3'><label class='form-label'>Ville de départ</label>" +
               "<select class='form-select'><option>Paris</option><option>Lyon</option><option>Marseille</option><option>Bordeaux</option></select></div>" +
               "<div class='col-md-6 mb-3'><label class='form-label'>Ville d'arrivée</label>" +
               "<select class='form-select'><option>Lyon</option><option>Paris</option><option>Marseille</option><option>Toulouse</option></select></div></div>" +
               "<div class='mb-3'><label class='form-label'>Date</label><input type='date' class='form-control'></div>" +
               "<button type='button' class='btn btn-primary' onclick='showResults()'>Rechercher</button>" +
               "</form><div id='results' style='display:none' class='mt-4'>" +
               "<h5>Résultats de recherche</h5><div class='alert alert-success'>" +
               "3 voyages trouvés pour Paris → Lyon</div>" +
               "<div class='card mb-2'><div class='card-body'><h6>Paris → Lyon</h6>" +
               "<p>Départ: 08:00 - Arrivée: 12:30<br>Prix: 89,50€ - Places disponibles: 150</p>" +
               "<button class='btn btn-outline-primary' onclick='alert(\"Réservation disponible en mode complet\")'>Réserver</button>" +
               "</div></div></div><hr><a href='/Train' class='btn btn-outline-secondary'>Retour</a>" +
               "</div></div></div></div></div>" +
               "<script>function showResults(){document.getElementById('results').style.display='block';}</script>" +
               "</body></html>";
    }
}

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Accueil</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        .feature-card {
            transition: transform 0.3s;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="${pageContext.request.contextPath}/">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/search">Rechercher</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <c:choose>
                        <c:when test="${empty sessionScope.user}">
                            <li class="nav-item">
                                <a class="nav-link" href="${pageContext.request.contextPath}/login">Connexion</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="${pageContext.request.contextPath}/register">Inscription</a>
                            </li>
                        </c:when>
                        <c:otherwise>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i> ${sessionScope.user.prenom}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/profile">Mon Profil</a></li>
                                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/reservation">Mes Réservations</a></li>
                                    <c:if test="${sessionScope.user.admin}">
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="${pageContext.request.contextPath}/admin">Administration</a></li>
                                    </c:if>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">Déconnexion</a></li>
                                </ul>
                            </li>
                        </c:otherwise>
                    </c:choose>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">Bienvenue sur TrainSystem</h1>
            <p class="lead mb-5">Votre plateforme de réservation de billets de train en ligne</p>
            
            <!-- Status de l'application -->
            <div class="alert alert-success d-inline-block mb-4">
                <i class="fas fa-check-circle me-2"></i>
                Application déployée avec succès !
            </div>
            
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-dark">Rechercher un trajet</h5>
                            <form action="${pageContext.request.contextPath}/search" method="get">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="villeDepart" placeholder="Ville de départ" required>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="villeArrivee" placeholder="Ville d'arrivée" required>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="date" class="form-control" name="dateVoyage" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-search"></i> Rechercher
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Test Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-4">
                <div class="col">
                    <h2 class="fw-bold">Test de l'Application</h2>
                    <p class="text-muted">Vérifiez que tous les composants fonctionnent</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-database fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Base de Données</h5>
                            <a href="${pageContext.request.contextPath}/test-db" class="btn btn-outline-primary">Tester</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-user-plus fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Inscription</h5>
                            <a href="${pageContext.request.contextPath}/register" class="btn btn-outline-success">Tester</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-sign-in-alt fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">Connexion</h5>
                            <a href="${pageContext.request.contextPath}/login" class="btn btn-outline-warning">Tester</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-search fa-3x text-info mb-3"></i>
                            <h5 class="card-title">Recherche</h5>
                            <a href="${pageContext.request.contextPath}/search" class="btn btn-outline-info">Tester</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col">
                    <h2 class="fw-bold">Nos Services</h2>
                    <p class="text-muted">Découvrez tout ce que nous offrons pour faciliter vos voyages</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 feature-card">
                        <div class="card-body text-center">
                            <i class="fas fa-search fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Recherche Facile</h5>
                            <p class="card-text">Trouvez rapidement les trajets qui vous conviennent avec notre moteur de recherche avancé.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 feature-card">
                        <div class="card-body text-center">
                            <i class="fas fa-ticket-alt fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Réservation Instantanée</h5>
                            <p class="card-text">Réservez vos billets en quelques clics et recevez votre confirmation immédiatement.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 feature-card">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">Paiement Sécurisé</h5>
                            <p class="card-text">Vos transactions sont protégées par nos systèmes de sécurité de pointe.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-train"></i> TrainSystem</h5>
                    <p class="mb-0">Votre partenaire de confiance pour tous vos voyages en train.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2025 TrainSystem. Tous droits réservés.</p>
                    <small class="text-muted">Version 1.0 - Déployé avec succès</small>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Définir la date minimale à aujourd'hui
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.querySelector('input[name="dateVoyage"]');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.min = today;
                dateInput.value = today;
            }
        });
    </script>
</body>
</html>

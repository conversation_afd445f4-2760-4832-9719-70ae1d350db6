<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recherche - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <div class="navbar-nav ms-auto">
                <c:choose>
                    <c:when test="${empty sessionScope.user}">
                        <a class="nav-link" href="${pageContext.request.contextPath}/login">Connexion</a>
                    </c:when>
                    <c:otherwise>
                        <span class="navbar-text me-3">Bonjour ${sessionScope.user.prenom}</span>
                        <a class="nav-link" href="${pageContext.request.contextPath}/reservation">Mes Réservations</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-search"></i> Rechercher un Voyage</h3>
                    </div>
                    <div class="card-body">
                        <!-- Messages -->
                        <c:if test="${not empty errorMessage}">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> ${errorMessage}
                            </div>
                        </c:if>

                        <form action="${pageContext.request.contextPath}/search" method="post">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="villeDepart" class="form-label">Ville de départ</label>
                                    <input type="text" class="form-control" id="villeDepart" name="villeDepart" 
                                           value="${villeDepart}" required placeholder="Ex: Paris">
                                    <div class="form-text">Villes disponibles: Paris, Lyon, Bordeaux, Marseille, Toulouse</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="villeArrivee" class="form-label">Ville d'arrivée</label>
                                    <input type="text" class="form-control" id="villeArrivee" name="villeArrivee" 
                                           value="${villeArrivee}" required placeholder="Ex: Lyon">
                                </div>
                                <div class="col-12">
                                    <label for="dateVoyage" class="form-label">Date du voyage</label>
                                    <input type="date" class="form-control" id="dateVoyage" name="dateVoyage" 
                                           value="${dateVoyage}" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-search"></i> Rechercher
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Exemples de recherche -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-lightbulb"></i> Exemples de recherche</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <a href="${pageContext.request.contextPath}/search?villeDepart=Paris&villeArrivee=Lyon&dateVoyage=${today}" 
                                   class="btn btn-outline-primary w-100 mb-2">
                                    Paris → Lyon
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="${pageContext.request.contextPath}/search?villeDepart=Lyon&villeArrivee=Marseille&dateVoyage=${today}" 
                                   class="btn btn-outline-primary w-100 mb-2">
                                    Lyon → Marseille
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="${pageContext.request.contextPath}/search?villeDepart=Paris&villeArrivee=Bordeaux&dateVoyage=${today}" 
                                   class="btn btn-outline-primary w-100 mb-2">
                                    Paris → Bordeaux
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Définir la date minimale à aujourd'hui
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.getElementById('dateVoyage');
            const today = new Date().toISOString().split('T')[0];
            dateInput.min = today;
            if (!dateInput.value) {
                dateInput.value = today;
            }
        });
    </script>
</body>
</html>

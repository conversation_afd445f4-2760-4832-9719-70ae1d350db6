<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Résultats de recherche - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .voyage-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border: 1px solid #e0e0e0;
        }
        .voyage-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .price-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .search-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <div class="navbar-nav ms-auto">
                <c:choose>
                    <c:when test="${empty sessionScope.user}">
                        <a class="nav-link" href="${pageContext.request.contextPath}/login">Connexion</a>
                    </c:when>
                    <c:otherwise>
                        <div class="dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> ${sessionScope.user.prenom}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="${pageContext.request.contextPath}/reservation">Mes Réservations</a></li>
                                <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">Déconnexion</a></li>
                            </ul>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- Résumé de la recherche -->
        <div class="search-summary p-4 mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4 class="mb-2">
                        <i class="fas fa-search text-primary me-2"></i>
                        ${villeDepart} → ${villeArrivee}
                    </h4>
                    <p class="mb-0 text-muted">
                        <i class="fas fa-calendar me-2"></i>
                        <fmt:formatDate value="${dateVoyage}" pattern="EEEE dd MMMM yyyy" />
                        • ${nombreResultats} voyage(s) trouvé(s)
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="${pageContext.request.contextPath}/search" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Modifier la recherche
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <c:if test="${not empty successMessage}">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${successMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </c:if>

        <c:if test="${not empty infoMessage}">
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                ${infoMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </c:if>

        <!-- Résultats -->
        <c:choose>
            <c:when test="${empty voyages}">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-4x text-muted mb-3"></i>
                    <h3>Aucun voyage trouvé</h3>
                    <p class="text-muted mb-4">
                        Nous n'avons trouvé aucun voyage pour votre recherche.<br>
                        Essayez de modifier vos critères de recherche.
                    </p>
                    <a href="${pageContext.request.contextPath}/search" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Nouvelle recherche
                    </a>
                </div>
            </c:when>
            <c:otherwise>
                <div class="row">
                    <c:forEach var="voyage" items="${voyages}">
                        <div class="col-12 mb-3">
                            <div class="card voyage-card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <h5 class="mb-1">${voyage.trajet.gareDepart.ville}</h5>
                                            <p class="text-muted mb-0">
                                                <fmt:formatDate value="${voyage.trajet.heureDepart}" pattern="HH:mm" type="time" />
                                            </p>
                                            <small class="text-muted">${voyage.trajet.gareDepart.nom}</small>
                                        </div>
                                        
                                        <div class="col-md-2 text-center">
                                            <i class="fas fa-arrow-right text-primary fa-2x"></i>
                                            <br>
                                            <small class="text-muted">${voyage.trajet.dureeFormatee}</small>
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <h5 class="mb-1">${voyage.trajet.gareArrivee.ville}</h5>
                                            <p class="text-muted mb-0">
                                                <fmt:formatDate value="${voyage.trajet.heureArrivee}" pattern="HH:mm" type="time" />
                                            </p>
                                            <small class="text-muted">${voyage.trajet.gareArrivee.nom}</small>
                                        </div>
                                        
                                        <div class="col-md-2 text-center">
                                            <div class="price-badge text-white p-2 rounded">
                                                <strong><fmt:formatNumber value="${voyage.trajet.prix}" type="currency" currencySymbol="€" /></strong>
                                            </div>
                                            <small class="text-muted d-block mt-1">
                                                ${voyage.placesDisponibles} place(s) disponible(s)
                                            </small>
                                        </div>
                                        
                                        <div class="col-md-2 text-end">
                                            <c:choose>
                                                <c:when test="${voyage.placesDisponibles > 0}">
                                                    <c:choose>
                                                        <c:when test="${not empty sessionScope.user}">
                                                            <a href="${pageContext.request.contextPath}/reservation/create/${voyage.id}" 
                                                               class="btn btn-primary">
                                                                <i class="fas fa-ticket-alt me-2"></i>Réserver
                                                            </a>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <a href="${pageContext.request.contextPath}/login?redirectUrl=${pageContext.request.contextPath}/reservation/create/${voyage.id}" 
                                                               class="btn btn-primary">
                                                                <i class="fas fa-ticket-alt me-2"></i>Réserver
                                                            </a>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </c:when>
                                                <c:otherwise>
                                                    <button class="btn btn-secondary" disabled>
                                                        <i class="fas fa-times me-2"></i>Complet
                                                    </button>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                    
                                    <!-- Informations supplémentaires -->
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="badge bg-light text-dark me-2">
                                                        <i class="fas fa-users me-1"></i>
                                                        ${voyage.totalPlaces - voyage.placesDisponibles}/${voyage.totalPlaces} occupé
                                                    </span>
                                                    <c:if test="${voyage.tauxOccupation > 80}">
                                                        <span class="badge bg-warning">
                                                            <i class="fas fa-exclamation-triangle me-1"></i>Presque complet
                                                        </span>
                                                    </c:if>
                                                </div>
                                                <div>
                                                    <small class="text-muted">
                                                        Voyage #${voyage.id}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:forEach>
                </div>
            </c:otherwise>
        </c:choose>

        <!-- Pagination (si nécessaire) -->
        <c:if test="${not empty voyages and voyages.size() > 10}">
            <nav aria-label="Navigation des résultats" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">Précédent</a>
                    </li>
                    <li class="page-item active">
                        <a class="page-link" href="#">1</a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#">Suivant</a>
                    </li>
                </ul>
            </nav>
        </c:if>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-train"></i> TrainSystem</h5>
                    <p class="mb-0">Votre partenaire de confiance pour tous vos voyages en train.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2025 TrainSystem. Tous droits réservés.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>

<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .register-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem 0;
        }
        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .password-strength {
            height: 5px;
            border-radius: 3px;
            transition: all 0.3s ease;
        }
        .strength-weak { background-color: #dc3545; }
        .strength-medium { background-color: #ffc107; }
        .strength-strong { background-color: #28a745; }
    </style>
</head>
<body>
    <div class="register-container d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="register-card p-4">
                        <div class="text-center mb-4">
                            <i class="fas fa-train fa-3x text-primary mb-3"></i>
                            <h2 class="fw-bold">Créer un compte</h2>
                            <p class="text-muted">Rejoignez TrainSystem pour réserver vos billets</p>
                        </div>

                        <!-- Messages d'erreur -->
                        <c:if test="${not empty errorMessage}">
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                ${errorMessage}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        </c:if>

                        <form action="${pageContext.request.contextPath}/register" method="post" id="registerForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="prenom" class="form-label">
                                        <i class="fas fa-user me-2"></i>Prénom *
                                    </label>
                                    <input type="text" class="form-control" id="prenom" name="prenom" 
                                           value="${prenom}" required placeholder="Votre prénom">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="nom" class="form-label">
                                        <i class="fas fa-user me-2"></i>Nom *
                                    </label>
                                    <input type="text" class="form-control" id="nom" name="nom" 
                                           value="${nom}" required placeholder="Votre nom">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>Email *
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="${email}" required placeholder="<EMAIL>">
                                <div class="form-text">Nous ne partagerons jamais votre email.</div>
                            </div>

                            <div class="mb-3">
                                <label for="telephone" class="form-label">
                                    <i class="fas fa-phone me-2"></i>Téléphone
                                </label>
                                <input type="tel" class="form-control" id="telephone" name="telephone" 
                                       value="${telephone}" placeholder="06 12 34 56 78">
                                <div class="form-text">Format français (optionnel)</div>
                            </div>

                            <div class="mb-3">
                                <label for="motDePasse" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Mot de passe *
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="motDePasse" name="motDePasse" 
                                           required placeholder="Votre mot de passe">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength mt-2" id="passwordStrength"></div>
                                <div class="form-text">
                                    Minimum 8 caractères avec majuscules, minuscules, chiffres et caractères spéciaux
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="confirmationMotDePasse" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Confirmer le mot de passe *
                                </label>
                                <input type="password" class="form-control" id="confirmationMotDePasse" 
                                       name="confirmationMotDePasse" required placeholder="Confirmez votre mot de passe">
                                <div class="invalid-feedback" id="passwordMismatch">
                                    Les mots de passe ne correspondent pas
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="acceptTerms" required>
                                <label class="form-check-label" for="acceptTerms">
                                    J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a> 
                                    et la <a href="#" class="text-decoration-none">politique de confidentialité</a> *
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <i class="fas fa-user-plus me-2"></i>Créer mon compte
                                </button>
                            </div>
                        </form>

                        <div class="text-center">
                            <p class="mb-0">
                                Déjà un compte ? 
                                <a href="${pageContext.request.contextPath}/login" class="text-decoration-none fw-bold">
                                    Se connecter
                                </a>
                            </p>
                        </div>

                        <hr class="my-4">

                        <div class="text-center">
                            <a href="${pageContext.request.contextPath}/home" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Retour à l'accueil
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('motDePasse');
            const toggleIcon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        });

        // Password strength indicator
        document.getElementById('motDePasse').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');
            
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            strengthBar.className = 'password-strength mt-2';
            if (strength < 3) {
                strengthBar.classList.add('strength-weak');
            } else if (strength < 5) {
                strengthBar.classList.add('strength-medium');
            } else {
                strengthBar.classList.add('strength-strong');
            }
        });

        // Password confirmation validation
        function validatePasswordMatch() {
            const password = document.getElementById('motDePasse').value;
            const confirmPassword = document.getElementById('confirmationMotDePasse').value;
            const mismatchDiv = document.getElementById('passwordMismatch');
            const confirmField = document.getElementById('confirmationMotDePasse');
            
            if (confirmPassword && password !== confirmPassword) {
                confirmField.classList.add('is-invalid');
                mismatchDiv.style.display = 'block';
                return false;
            } else {
                confirmField.classList.remove('is-invalid');
                mismatchDiv.style.display = 'none';
                return true;
            }
        }

        document.getElementById('confirmationMotDePasse').addEventListener('input', validatePasswordMatch);
        document.getElementById('motDePasse').addEventListener('input', validatePasswordMatch);

        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            if (!validatePasswordMatch()) {
                e.preventDefault();
                return false;
            }
        });

        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>

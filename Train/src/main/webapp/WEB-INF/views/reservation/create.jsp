<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réserver - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Bonjour ${sessionScope.user.prenom}</span>
                <a class="nav-link" href="${pageContext.request.contextPath}/search">Rechercher</a>
                <a class="nav-link" href="${pageContext.request.contextPath}/reservation">Mes Réservations</a>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-ticket-alt"></i> Réserver votre voyage</h3>
                    </div>
                    <div class="card-body">
                        <!-- Messages -->
                        <c:if test="${not empty errorMessage}">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> ${errorMessage}
                            </div>
                        </c:if>

                        <!-- Détails du voyage -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h5 class="card-title">Détails du voyage</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Départ :</strong> ${voyage.trajet.gareDepart.ville}</p>
                                        <p><strong>Arrivée :</strong> ${voyage.trajet.gareArrivee.ville}</p>
                                        <p><strong>Date :</strong> 
                                            <fmt:formatDate value="${voyage.dateVoyage}" pattern="EEEE dd MMMM yyyy" />
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Heure de départ :</strong> 
                                            <fmt:formatDate value="${voyage.trajet.heureDepart}" pattern="HH:mm" type="time" />
                                        </p>
                                        <p><strong>Heure d'arrivée :</strong> 
                                            <fmt:formatDate value="${voyage.trajet.heureArrivee}" pattern="HH:mm" type="time" />
                                        </p>
                                        <p><strong>Prix par place :</strong> 
                                            <fmt:formatNumber value="${voyage.trajet.prix}" type="currency" currencySymbol="€" />
                                        </p>
                                    </div>
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> 
                                    ${voyage.placesDisponibles} place(s) disponible(s)
                                </div>
                            </div>
                        </div>

                        <!-- Formulaire de réservation -->
                        <form action="${pageContext.request.contextPath}/reservation/create/${voyage.id}" method="post">
                            <div class="mb-3">
                                <label for="nombrePlaces" class="form-label">Nombre de places</label>
                                <select class="form-select" id="nombrePlaces" name="nombrePlaces" required>
                                    <option value="">Choisissez le nombre de places</option>
                                    <c:forEach var="i" begin="1" end="${voyage.placesDisponibles > 10 ? 10 : voyage.placesDisponibles}">
                                        <option value="${i}">${i} place(s)</option>
                                    </c:forEach>
                                </select>
                            </div>

                            <div class="mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Récapitulatif</h6>
                                        <div class="row">
                                            <div class="col-6">Prix unitaire :</div>
                                            <div class="col-6 text-end">
                                                <fmt:formatNumber value="${voyage.trajet.prix}" type="currency" currencySymbol="€" />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">Nombre de places :</div>
                                            <div class="col-6 text-end" id="selectedPlaces">-</div>
                                        </div>
                                        <hr>
                                        <div class="row fw-bold">
                                            <div class="col-6">Total :</div>
                                            <div class="col-6 text-end" id="totalPrice">0,00 €</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="acceptConditions" required>
                                    <label class="form-check-label" for="acceptConditions">
                                        J'accepte les <a href="#" target="_blank">conditions générales de vente</a>
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="reserveBtn" disabled>
                                    <i class="fas fa-credit-card"></i> Confirmer la réservation
                                </button>
                                <a href="${pageContext.request.contextPath}/search" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Retour à la recherche
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const prixUnitaire = ${voyage.trajet.prix};
        const nombrePlacesSelect = document.getElementById('nombrePlaces');
        const selectedPlacesSpan = document.getElementById('selectedPlaces');
        const totalPriceSpan = document.getElementById('totalPrice');
        const acceptConditions = document.getElementById('acceptConditions');
        const reserveBtn = document.getElementById('reserveBtn');

        function updateTotal() {
            const nombrePlaces = parseInt(nombrePlacesSelect.value) || 0;
            const total = nombrePlaces * prixUnitaire;
            
            selectedPlacesSpan.textContent = nombrePlaces > 0 ? nombrePlaces : '-';
            totalPriceSpan.textContent = total.toLocaleString('fr-FR', {
                style: 'currency',
                currency: 'EUR'
            });
            
            updateReserveButton();
        }

        function updateReserveButton() {
            const nombrePlaces = parseInt(nombrePlacesSelect.value) || 0;
            const conditionsAccepted = acceptConditions.checked;
            
            reserveBtn.disabled = !(nombrePlaces > 0 && conditionsAccepted);
        }

        nombrePlacesSelect.addEventListener('change', updateTotal);
        acceptConditions.addEventListener('change', updateReserveButton);

        // Initialisation
        updateTotal();
    </script>
</body>
</html>

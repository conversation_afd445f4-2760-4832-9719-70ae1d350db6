@echo off
echo ========================================
echo    TRAINSYSTEM AVEC JETTY
echo ========================================
echo.

cd /d "%~dp0"

echo 1. Nettoyage des processus...
taskkill /F /IM java.exe 2>nul
timeout /t 2 /nobreak >nul

echo 2. Compilation...
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)
echo ✅ Compilation réussie

echo 3. Démarrage avec Jetty...
echo.
echo 🚀 Lancement de TrainSystem avec Jetty...
echo 📍 URL: http://localhost:8080/Train
echo 🔑 Compte: <EMAIL> / password
echo.
echo ⚠️ Pour arrêter: Ctrl+C dans la fenêtre du serveur
echo.

start "TrainSystem Jetty Server" cmd /k "echo ========================================= && echo    SERVEUR TRAINSYSTEM JETTY && echo ========================================= && echo. && echo ✅ Application: http://localhost:8080/Train && echo 🔑 Compte test: <EMAIL> / password && echo. && echo ⚠️ Pour arreter le serveur: Ctrl+C && echo. && mvn jetty:run"

echo ⏳ Attente du démarrage (25 secondes)...
timeout /t 25 /nobreak >nul

echo 🌐 Ouverture de l'application...
start http://localhost:8080/Train

echo.
echo ========================================
echo    APPLICATION LANCEE !
echo ========================================
echo.
echo ✅ TrainSystem est accessible sur :
echo    👉 http://localhost:8080/Train
echo.
echo 🎯 Fonctionnalités disponibles :
echo    ✅ Page d'accueil avec recherche
echo    ✅ Inscription et connexion
echo    ✅ Recherche de voyages
echo    ✅ Réservation de billets
echo    ✅ Gestion des réservations
echo.
echo 👤 Compte de test :
echo    📧 Email: <EMAIL>
echo    🔑 Password: password
echo.
echo 🛑 Pour arrêter l'application :
echo    • Fermez la fenêtre "TrainSystem Jetty Server"
echo    • Ou utilisez Ctrl+C dans cette fenêtre
echo.

pause

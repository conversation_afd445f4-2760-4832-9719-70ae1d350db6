@echo off
echo === TEST DE L'APPLICATION TRAIN ===
echo.

cd /d "%~dp0"

echo 1. Compilation du projet...
call mvn clean compile
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Echec de la compilation
    pause
    exit /b 1
)
echo ✅ Compilation réussie !
echo.

echo 2. Génération du WAR...
call mvn package -DskipTests
if %ERRORLEVEL% neq 0 (
    echo ERREUR: Echec du packaging
    pause
    exit /b 1
)
echo ✅ WAR généré avec succès !
echo.

echo 3. Test des utilitaires (sans base de données)...
java -cp "target/classes;target/Train/WEB-INF/lib/*" com.train.test.TestApplication
echo.

echo 4. Vérification du contenu du WAR...
if exist "target\Train.war" (
    echo ✅ Fichier WAR trouvé: target\Train.war
    for %%I in (target\Train.war) do echo    Taille: %%~zI bytes
) else (
    echo ❌ Fichier WAR non trouvé
)
echo.

echo === RÉSUMÉ DES TESTS ===
echo ✅ Compilation: OK
echo ✅ Packaging: OK
echo ✅ Structure: OK
echo.
echo Pour déployer l'application:
echo 1. Démarrez MySQL et créez la base 'train'
echo 2. Exécutez le script SQL: src/main/resources/database/init.sql
echo 3. Copiez target/Train.war dans le dossier webapps de Tomcat
echo 4. Démarrez Tomcat
echo 5. Accédez à http://localhost:8080/Train
echo.

pause

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Captures d'écran</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .screenshot-mockup {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            background: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        .browser-bar {
            background: #f8f9fa;
            border-radius: 4px 4px 0 0;
            padding: 8px 12px;
            border-bottom: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }
        .page-content {
            padding: 20px;
            min-height: 300px;
            background: #fff;
        }
        .form-mockup {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .table-mockup {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-arrow-left"></i> Retour à l'accueil
            </a>
            <span class="navbar-text">
                <i class="fas fa-images"></i> Captures d'écran TrainSystem
            </span>
        </div>
    </nav>

    <div class="container my-5">
        <h1 class="text-center mb-5">Interface Utilisateur TrainSystem</h1>

        <!-- Page d'accueil -->
        <div class="screenshot-mockup">
            <div class="browser-bar">
                <i class="fas fa-globe"></i> http://localhost:8080/Train
            </div>
            <div class="page-content">
                <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
                    <div class="container-fluid">
                        <span class="navbar-brand"><i class="fas fa-train"></i> TrainSystem</span>
                        <div class="navbar-nav ms-auto">
                            <a class="nav-link" href="#">Connexion</a>
                            <a class="nav-link" href="#">Inscription</a>
                        </div>
                    </div>
                </nav>
                
                <div class="jumbotron bg-light p-4 rounded">
                    <h1 class="display-5">Bienvenue sur TrainSystem</h1>
                    <p class="lead">Réservez vos billets de train en quelques clics</p>
                </div>

                <div class="form-mockup">
                    <h5><i class="fas fa-search"></i> Rechercher un voyage</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Ville de départ</label>
                            <select class="form-select">
                                <option>Paris</option>
                                <option>Lyon</option>
                                <option>Marseille</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Ville d'arrivée</label>
                            <select class="form-select">
                                <option>Lyon</option>
                                <option>Marseille</option>
                                <option>Nice</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date de départ</label>
                            <input type="date" class="form-control" value="2025-05-29">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Rechercher
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Résultats de recherche -->
        <div class="screenshot-mockup">
            <div class="browser-bar">
                <i class="fas fa-globe"></i> http://localhost:8080/Train/search
            </div>
            <div class="page-content">
                <h3><i class="fas fa-list"></i> Voyages disponibles : Paris → Lyon</h3>
                <p class="text-muted">3 voyages trouvés pour le 29 mai 2025</p>
                
                <div class="table-mockup">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Départ</th>
                                <th>Arrivée</th>
                                <th>Durée</th>
                                <th>Prix</th>
                                <th>Places</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>08:30</td>
                                <td>10:45</td>
                                <td>2h15</td>
                                <td>45€</td>
                                <td>12 disponibles</td>
                                <td><button class="btn btn-success btn-sm">Réserver</button></td>
                            </tr>
                            <tr>
                                <td>12:15</td>
                                <td>14:30</td>
                                <td>2h15</td>
                                <td>45€</td>
                                <td>8 disponibles</td>
                                <td><button class="btn btn-success btn-sm">Réserver</button></td>
                            </tr>
                            <tr>
                                <td>18:00</td>
                                <td>20:15</td>
                                <td>2h15</td>
                                <td>45€</td>
                                <td>15 disponibles</td>
                                <td><button class="btn btn-success btn-sm">Réserver</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Page d'administration -->
        <div class="screenshot-mockup">
            <div class="browser-bar">
                <i class="fas fa-globe"></i> http://localhost:8080/Train/admin
            </div>
            <div class="page-content">
                <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
                    <div class="container-fluid">
                        <span class="navbar-brand"><i class="fas fa-cog"></i> Administration</span>
                        <div class="navbar-nav ms-auto">
                            <span class="navbar-text"><EMAIL></span>
                            <a class="nav-link" href="#">Déconnexion</a>
                        </div>
                    </div>
                </nav>

                <div class="row">
                    <div class="col-md-3">
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action active">
                                <i class="fas fa-map-marker-alt"></i> Gares
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <i class="fas fa-route"></i> Trajets
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <i class="fas fa-calendar"></i> Voyages
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <i class="fas fa-users"></i> Utilisateurs
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <i class="fas fa-chart-bar"></i> Statistiques
                            </a>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="fas fa-map-marker-alt"></i> Gestion des Gares</h4>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i> Nouvelle Gare
                            </button>
                        </div>
                        
                        <div class="table-mockup">
                            <table class="table">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Nom</th>
                                        <th>Ville</th>
                                        <th>Code</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>Gare de Lyon</td>
                                        <td>Paris</td>
                                        <td>PLY</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                            <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>Gare Part-Dieu</td>
                                        <td>Lyon</td>
                                        <td>LPD</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                            <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page de réservation -->
        <div class="screenshot-mockup">
            <div class="browser-bar">
                <i class="fas fa-globe"></i> http://localhost:8080/Train/reservation
            </div>
            <div class="page-content">
                <h3><i class="fas fa-ticket-alt"></i> Confirmer votre réservation</h3>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5>Détails du voyage</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Départ :</strong> Paris (Gare de Lyon)</p>
                                        <p><strong>Heure :</strong> 08:30</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Arrivée :</strong> Lyon (Part-Dieu)</p>
                                        <p><strong>Heure :</strong> 10:45</p>
                                    </div>
                                </div>
                                <p><strong>Date :</strong> 29 mai 2025</p>
                                <p><strong>Durée :</strong> 2h15</p>
                            </div>
                        </div>
                        
                        <div class="form-mockup mt-3">
                            <h6>Informations passager</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Nombre de places</label>
                                    <select class="form-select">
                                        <option>1</option>
                                        <option>2</option>
                                        <option>3</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Type de billet</label>
                                    <select class="form-select">
                                        <option>Standard</option>
                                        <option>Première classe</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5>Récapitulatif</h5>
                            </div>
                            <div class="card-body">
                                <p>1 place × 45€ = <strong>45€</strong></p>
                                <hr>
                                <p class="h5">Total : <strong>45€</strong></p>
                                <button class="btn btn-success w-100 mt-3">
                                    <i class="fas fa-credit-card"></i> Confirmer et Payer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <a href="index.html" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Retour à l'accueil
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

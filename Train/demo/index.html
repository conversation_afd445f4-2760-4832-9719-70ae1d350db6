<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Système de Gestion des Trains</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .tech-badge {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            padding: 5px 15px;
            margin: 5px;
            display: inline-block;
        }
        .demo-section {
            background: #f8f9fa;
            padding: 60px 0;
        }
        .code-preview {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#"><i class="fas fa-train"></i> TrainSystem</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="#features">Fonctionnalités</a></li>
                    <li class="nav-item"><a class="nav-link" href="#tech">Technologies</a></li>
                    <li class="nav-item"><a class="nav-link" href="#demo">Démonstration</a></li>
                    <li class="nav-item"><a class="nav-link" href="#structure">Structure</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-train"></i> TrainSystem
                    </h1>
                    <p class="lead mb-4">
                        Système complet de gestion des réservations de billets de train développé en Java JEE
                    </p>
                    <div class="mb-4">
                        <span class="badge bg-success fs-6 me-2">
                            <i class="fas fa-check"></i> Application Fonctionnelle
                        </span>
                        <span class="badge bg-info fs-6">
                            <i class="fas fa-code"></i> Code Source Complet
                        </span>
                    </div>
                    <div class="d-flex gap-3">
                        <a href="#demo" class="btn btn-light btn-lg">
                            <i class="fas fa-play"></i> Voir la Démo
                        </a>
                        <a href="screenshots.html" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-images"></i> Captures d'écran
                        </a>
                        <a href="#structure" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-code-branch"></i> Explorer le Code
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-train" style="font-size: 200px; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Fonctionnalités Principales</h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Espace Client</h5>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success"></i> Recherche de trajets</li>
                                <li><i class="fas fa-check text-success"></i> Réservation de billets</li>
                                <li><i class="fas fa-check text-success"></i> Gestion du profil</li>
                                <li><i class="fas fa-check text-success"></i> Historique des réservations</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-cog fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">Administration</h5>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success"></i> Gestion des trajets</li>
                                <li><i class="fas fa-check text-success"></i> Gestion des gares</li>
                                <li><i class="fas fa-check text-success"></i> Programmation des voyages</li>
                                <li><i class="fas fa-check text-success"></i> Statistiques et rapports</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-3x text-danger mb-3"></i>
                            <h5 class="card-title">Sécurité</h5>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-check text-success"></i> Authentification sécurisée</li>
                                <li><i class="fas fa-check text-success"></i> Hachage BCrypt</li>
                                <li><i class="fas fa-check text-success"></i> Filtres de sécurité</li>
                                <li><i class="fas fa-check text-success"></i> Validation des données</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technologies Section -->
    <section id="tech" class="demo-section">
        <div class="container">
            <h2 class="text-center mb-5">Technologies Utilisées</h2>
            <div class="row">
                <div class="col-md-6">
                    <h4><i class="fas fa-server text-primary"></i> Backend</h4>
                    <div class="mb-4">
                        <span class="tech-badge"><i class="fab fa-java"></i> Java 11</span>
                        <span class="tech-badge"><i class="fas fa-globe"></i> JEE (Servlet/JSP)</span>
                        <span class="tech-badge"><i class="fas fa-tags"></i> JSTL/EL</span>
                        <span class="tech-badge"><i class="fas fa-database"></i> MySQL 8.0</span>
                        <span class="tech-badge"><i class="fas fa-tools"></i> Maven</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <h4><i class="fas fa-paint-brush text-success"></i> Frontend</h4>
                    <div class="mb-4">
                        <span class="tech-badge"><i class="fab fa-html5"></i> JSP</span>
                        <span class="tech-badge"><i class="fab fa-bootstrap"></i> Bootstrap 5</span>
                        <span class="tech-badge"><i class="fas fa-icons"></i> Font Awesome</span>
                        <span class="tech-badge"><i class="fab fa-js"></i> JavaScript</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Démonstration du Projet</h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-desktop"></i> Interface Utilisateur</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Informations de Connexion</h6>
                                <p class="mb-1"><strong>Administrateur :</strong></p>
                                <p class="mb-1">Email : <code><EMAIL></code></p>
                                <p class="mb-0">Mot de passe : <code>password</code></p>
                            </div>

                            <h6>Données de Test Incluses :</h6>
                            <ul>
                                <li>8 gares dans différentes villes françaises</li>
                                <li>Trajets bidirectionnels entre les gares</li>
                                <li>Voyages programmés pour les 7 prochains jours</li>
                                <li>Système de réservation fonctionnel</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Structure Section -->
    <section id="structure" class="demo-section">
        <div class="container">
            <h2 class="text-center mb-5">Structure du Projet</h2>
            <div class="row">
                <div class="col-lg-6">
                    <h4><i class="fas fa-folder-open text-warning"></i> Architecture</h4>
                    <div class="code-preview">
src/main/java/com/train/
├── model/          # Entités métier
│   ├── User.java
│   ├── Gare.java
│   ├── Trajet.java
│   ├── Voyage.java
│   └── Reservation.java
├── dao/            # Accès aux données
│   ├── UserDAO.java
│   ├── GareDAO.java
│   └── ...
├── service/        # Logique métier
│   ├── UserService.java
│   ├── VoyageService.java
│   └── ...
├── servlet/        # Contrôleurs web
│   ├── AuthServlet.java
│   ├── SearchServlet.java
│   └── ...
└── util/           # Utilitaires
    ├── DatabaseUtil.java
    └── SecurityUtil.java
                    </div>
                </div>
                <div class="col-lg-6">
                    <h4><i class="fas fa-cogs text-info"></i> Fonctionnalités Implémentées</h4>
                    <div class="list-group">
                        <div class="list-group-item">
                            <i class="fas fa-check text-success"></i> Architecture MVC complète
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-check text-success"></i> Couche DAO avec implémentations
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-check text-success"></i> Services métier complets
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-check text-success"></i> Interface web responsive
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-check text-success"></i> Système d'authentification
                        </div>
                        <div class="list-group-item">
                            <i class="fas fa-check text-success"></i> Gestion des réservations
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">© 2025 TrainSystem - Application JEE Complète</p>
            <p class="mb-0">
                <i class="fas fa-code"></i> Développé avec Java JEE, Bootstrap et MySQL
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

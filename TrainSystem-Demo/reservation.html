<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Réservation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .reservation-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px 0;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 15px;
        }
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #6c757d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        .step.active .step-number {
            background: #28a745;
        }
        .voyage-summary {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search.html">Rechercher</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html">Connexion</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="register.html">Inscription</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Reservation Header -->
    <section class="reservation-header">
        <div class="container">
            <h1 class="text-center mb-4">
                <i class="fas fa-ticket-alt"></i> Confirmer votre réservation
            </h1>
            <p class="text-center lead">Vérifiez les détails de votre voyage et finalisez votre réservation</p>
        </div>
    </section>

    <div class="container my-5">
        <!-- Step Indicator -->
        <div class="step-indicator">
            <div class="step active">
                <div class="step-number">1</div>
                <span>Sélection</span>
            </div>
            <div class="step active">
                <div class="step-number">2</div>
                <span>Réservation</span>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <span>Paiement</span>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Détails du voyage -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-route"></i> Détails du voyage</h5>
                    </div>
                    <div class="card-body voyage-summary">
                        <div class="row">
                            <div class="col-md-5">
                                <h6><i class="fas fa-map-marker-alt text-primary"></i> Départ</h6>
                                <p class="mb-1"><strong id="villeDepart">Paris</strong></p>
                                <p class="mb-1">Gare de Lyon</p>
                                <p class="mb-0"><strong id="heureDepart">08:30</strong></p>
                            </div>
                            <div class="col-md-2 text-center">
                                <i class="fas fa-arrow-right fa-2x text-success mt-3"></i>
                                <p class="text-muted mt-2">2h15</p>
                            </div>
                            <div class="col-md-5">
                                <h6><i class="fas fa-map-marker-alt text-danger"></i> Arrivée</h6>
                                <p class="mb-1"><strong id="villeArrivee">Lyon</strong></p>
                                <p class="mb-1">Part-Dieu</p>
                                <p class="mb-0"><strong id="heureArrivee">10:45</strong></p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Date :</strong> <span id="dateVoyage">29 mai 2025</span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Prix unitaire :</strong> <span id="prixUnitaire">45</span>€</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Formulaire de réservation -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user"></i> Informations de réservation</h5>
                    </div>
                    <div class="card-body">
                        <form id="reservationForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="nom" class="form-label">Nom *</label>
                                    <input type="text" class="form-control" id="nom" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="prenom" class="form-label">Prénom *</label>
                                    <input type="text" class="form-control" id="prenom" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="telephone" class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="telephone">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="nombrePlaces" class="form-label">Nombre de places *</label>
                                    <select class="form-select" id="nombrePlaces" required>
                                        <option value="">Sélectionnez</option>
                                        <option value="1">1 place</option>
                                        <option value="2">2 places</option>
                                        <option value="3">3 places</option>
                                        <option value="4">4 places</option>
                                        <option value="5">5 places</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="typePlace" class="form-label">Type de place</label>
                                    <select class="form-select" id="typePlace">
                                        <option value="standard">Standard</option>
                                        <option value="premiere">Première classe (+20€)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="acceptConditions" required>
                                <label class="form-check-label" for="acceptConditions">
                                    J'accepte les <a href="#" data-bs-toggle="modal" data-bs-target="#conditionsModal">conditions générales</a> *
                                </label>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    Je souhaite recevoir les offres promotionnelles par email
                                </label>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Récapitulatif -->
                <div class="card sticky-top">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-calculator"></i> Récapitulatif</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Places :</span>
                            <span id="selectedPlaces">-</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Prix unitaire :</span>
                            <span id="displayPrixUnitaire">45€</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Supplément :</span>
                            <span id="supplement">0€</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total :</strong>
                            <strong id="totalPrice">0€</strong>
                        </div>
                        
                        <button type="button" class="btn btn-success w-100 mb-3" id="reserveBtn" disabled onclick="proceedToPayment()">
                            <i class="fas fa-credit-card"></i> Procéder au paiement
                        </button>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt"></i> Paiement sécurisé<br>
                                <i class="fas fa-undo"></i> Annulation gratuite jusqu'à 2h avant
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Conditions -->
    <div class="modal fade" id="conditionsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Conditions Générales</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>1. Réservation</h6>
                    <p>La réservation est obligatoire pour tous les voyages. Le billet doit être présenté lors du contrôle.</p>
                    
                    <h6>2. Annulation</h6>
                    <p>L'annulation est gratuite jusqu'à 2 heures avant le départ. Passé ce délai, des frais s'appliquent.</p>
                    
                    <h6>3. Modification</h6>
                    <p>Les modifications sont possibles moyennant des frais de 5€ par billet.</p>
                    
                    <h6>4. Retard</h6>
                    <p>En cas de retard de plus de 30 minutes, un remboursement partiel peut être accordé.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">J'ai compris</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">© 2025 TrainSystem - Application JEE Complète</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Get URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Initialize page with URL parameters
        document.addEventListener('DOMContentLoaded', function() {
            const voyage = getUrlParameter('voyage');
            const depart = getUrlParameter('depart') || '08:30';
            const arrivee = getUrlParameter('arrivee') || '10:45';
            const prix = getUrlParameter('prix') || '45';
            
            document.getElementById('heureDepart').textContent = depart;
            document.getElementById('heureArrivee').textContent = arrivee;
            document.getElementById('prixUnitaire').textContent = prix;
            document.getElementById('displayPrixUnitaire').textContent = prix + '€';
            
            updateTotal();
        });

        // Update total calculation
        function updateTotal() {
            const nombrePlaces = parseInt(document.getElementById('nombrePlaces').value) || 0;
            const typePlace = document.getElementById('typePlace').value;
            const prixUnitaire = parseInt(document.getElementById('prixUnitaire').textContent);
            
            let supplement = 0;
            if (typePlace === 'premiere') {
                supplement = nombrePlaces * 20;
            }
            
            const total = (nombrePlaces * prixUnitaire) + supplement;
            
            document.getElementById('selectedPlaces').textContent = nombrePlaces > 0 ? nombrePlaces : '-';
            document.getElementById('supplement').textContent = supplement + '€';
            document.getElementById('totalPrice').textContent = total + '€';
            
            updateReserveButton();
        }

        // Update reserve button state
        function updateReserveButton() {
            const nombrePlaces = parseInt(document.getElementById('nombrePlaces').value) || 0;
            const conditionsAccepted = document.getElementById('acceptConditions').checked;
            const nom = document.getElementById('nom').value.trim();
            const prenom = document.getElementById('prenom').value.trim();
            const email = document.getElementById('email').value.trim();
            
            const isValid = nombrePlaces > 0 && conditionsAccepted && nom && prenom && email;
            document.getElementById('reserveBtn').disabled = !isValid;
        }

        // Event listeners
        document.getElementById('nombrePlaces').addEventListener('change', updateTotal);
        document.getElementById('typePlace').addEventListener('change', updateTotal);
        document.getElementById('acceptConditions').addEventListener('change', updateReserveButton);
        document.getElementById('nom').addEventListener('input', updateReserveButton);
        document.getElementById('prenom').addEventListener('input', updateReserveButton);
        document.getElementById('email').addEventListener('input', updateReserveButton);

        // Proceed to payment
        function proceedToPayment() {
            const formData = {
                nom: document.getElementById('nom').value,
                prenom: document.getElementById('prenom').value,
                email: document.getElementById('email').value,
                telephone: document.getElementById('telephone').value,
                nombrePlaces: document.getElementById('nombrePlaces').value,
                typePlace: document.getElementById('typePlace').value,
                total: document.getElementById('totalPrice').textContent
            };
            
            // Store data for payment page
            sessionStorage.setItem('paymentData', JSON.stringify(formData));
            
            // Redirect to payment page
            window.location.href = 'payment.html';
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Recherche</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .search-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
        }
        .example-btn {
            transition: all 0.3s ease;
        }
        .example-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="search.html">Rechercher</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html">Connexion</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="register.html">Inscription</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Search Header -->
    <section class="search-header">
        <div class="container">
            <h1 class="text-center mb-4">
                <i class="fas fa-search"></i> Rechercher un voyage
            </h1>
            <p class="text-center lead">Trouvez le trajet parfait pour votre voyage</p>
        </div>
    </section>

    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Formulaire de recherche -->
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-search"></i> Critères de recherche</h5>
                    </div>
                    <div class="card-body">
                        <form action="search-results.html" method="get" id="searchForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="villeDepart" class="form-label">Ville de départ</label>
                                    <select class="form-select" id="villeDepart" name="villeDepart" required>
                                        <option value="">Sélectionnez une ville</option>
                                        <option value="Paris">Paris</option>
                                        <option value="Lyon">Lyon</option>
                                        <option value="Marseille">Marseille</option>
                                        <option value="Bordeaux">Bordeaux</option>
                                        <option value="Toulouse">Toulouse</option>
                                        <option value="Nice">Nice</option>
                                        <option value="Nantes">Nantes</option>
                                        <option value="Strasbourg">Strasbourg</option>
                                    </select>
                                    <div class="form-text">Villes disponibles dans notre réseau</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="villeArrivee" class="form-label">Ville d'arrivée</label>
                                    <select class="form-select" id="villeArrivee" name="villeArrivee" required>
                                        <option value="">Sélectionnez une ville</option>
                                        <option value="Paris">Paris</option>
                                        <option value="Lyon">Lyon</option>
                                        <option value="Marseille">Marseille</option>
                                        <option value="Bordeaux">Bordeaux</option>
                                        <option value="Toulouse">Toulouse</option>
                                        <option value="Nice">Nice</option>
                                        <option value="Nantes">Nantes</option>
                                        <option value="Strasbourg">Strasbourg</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="dateVoyage" class="form-label">Date du voyage</label>
                                    <input type="date" class="form-control" id="dateVoyage" name="dateVoyage" required>
                                    <div class="form-text">Voyages disponibles pour les 7 prochains jours</div>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg px-5">
                                        <i class="fas fa-search"></i> Rechercher des voyages
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Exemples de recherche -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-lightbulb"></i> Exemples de recherche</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">Cliquez sur un exemple pour remplir automatiquement le formulaire :</p>
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 example-btn" 
                                        onclick="fillExample('Paris', 'Lyon')">
                                    <i class="fas fa-route"></i> Paris → Lyon
                                </button>
                            </div>
                            <div class="col-md-4 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 example-btn" 
                                        onclick="fillExample('Lyon', 'Marseille')">
                                    <i class="fas fa-route"></i> Lyon → Marseille
                                </button>
                            </div>
                            <div class="col-md-4 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 example-btn" 
                                        onclick="fillExample('Paris', 'Bordeaux')">
                                    <i class="fas fa-route"></i> Paris → Bordeaux
                                </button>
                            </div>
                            <div class="col-md-4 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 example-btn" 
                                        onclick="fillExample('Toulouse', 'Nice')">
                                    <i class="fas fa-route"></i> Toulouse → Nice
                                </button>
                            </div>
                            <div class="col-md-4 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 example-btn" 
                                        onclick="fillExample('Nantes', 'Paris')">
                                    <i class="fas fa-route"></i> Nantes → Paris
                                </button>
                            </div>
                            <div class="col-md-4 mb-2">
                                <button type="button" class="btn btn-outline-primary w-100 example-btn" 
                                        onclick="fillExample('Strasbourg', 'Lyon')">
                                    <i class="fas fa-route"></i> Strasbourg → Lyon
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations utiles -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Informations utiles</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-clock text-primary"></i> Horaires</h6>
                                <ul class="list-unstyled">
                                    <li>• Premiers départs : 06:00</li>
                                    <li>• Derniers départs : 22:00</li>
                                    <li>• Fréquence : toutes les 2-4 heures</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-euro-sign text-success"></i> Tarifs</h6>
                                <ul class="list-unstyled">
                                    <li>• Trajets courts : 25-45€</li>
                                    <li>• Trajets moyens : 45-75€</li>
                                    <li>• Trajets longs : 75-120€</li>
                                </ul>
                            </div>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-lightbulb"></i>
                            <strong>Astuce :</strong> Réservez à l'avance pour bénéficier des meilleurs tarifs !
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">© 2025 TrainSystem - Application JEE Complète</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set today's date as default
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.getElementById('dateVoyage');
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
            
            // Set min date to today
            dateInput.min = today;
            
            // Set max date to 7 days from now
            const maxDate = new Date();
            maxDate.setDate(maxDate.getDate() + 7);
            dateInput.max = maxDate.toISOString().split('T')[0];
        });

        // Fill example function
        function fillExample(depart, arrivee) {
            document.getElementById('villeDepart').value = depart;
            document.getElementById('villeArrivee').value = arrivee;
            
            // Add visual feedback
            const form = document.getElementById('searchForm');
            form.classList.add('border-primary');
            setTimeout(() => {
                form.classList.remove('border-primary');
            }, 1000);
        }

        // Form validation
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            const depart = document.getElementById('villeDepart').value;
            const arrivee = document.getElementById('villeArrivee').value;
            
            if (depart === arrivee) {
                e.preventDefault();
                alert('La ville de départ et d\'arrivée ne peuvent pas être identiques.');
                return false;
            }
        });
    </script>
</body>
</html>

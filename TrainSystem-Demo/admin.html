<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Administration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            background: #343a40;
            min-height: 100vh;
            padding-top: 20px;
        }
        .admin-sidebar .nav-link {
            color: #adb5bd;
            padding: 12px 20px;
            border-radius: 0;
        }
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            background: #495057;
            color: white;
        }
        .admin-content {
            padding: 20px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-card .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .table-actions .btn {
            margin-right: 5px;
        }
        .admin-header {
            background: #f8f9fa;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-shield"></i> Administrateur
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.html">Retour au site</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">Déconnexion</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                                <i class="fas fa-tachometer-alt"></i> Tableau de bord
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('gares')">
                                <i class="fas fa-map-marker-alt"></i> Gares
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('trajets')">
                                <i class="fas fa-route"></i> Trajets
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('voyages')">
                                <i class="fas fa-calendar"></i> Voyages
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('reservations')">
                                <i class="fas fa-ticket-alt"></i> Réservations
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('utilisateurs')">
                                <i class="fas fa-users"></i> Utilisateurs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('statistiques')">
                                <i class="fas fa-chart-bar"></i> Statistiques
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="admin-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 id="sectionTitle">Tableau de bord</h2>
                        <div class="text-muted">
                            <i class="fas fa-clock"></i> <span id="currentTime"></span>
                        </div>
                    </div>
                </div>

                <div class="admin-content">
                    <!-- Dashboard Section -->
                    <div id="dashboard" class="admin-section">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-number">8</div>
                                    <div>Gares actives</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-number">16</div>
                                    <div>Trajets configurés</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-number">42</div>
                                    <div>Voyages programmés</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-number">127</div>
                                    <div>Réservations actives</div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-chart-line"></i> Réservations récentes</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group list-group-flush">
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>Paris → Lyon</span>
                                                <span class="badge bg-success">Confirmée</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>Lyon → Marseille</span>
                                                <span class="badge bg-warning">En attente</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between">
                                                <span>Bordeaux → Paris</span>
                                                <span class="badge bg-success">Confirmée</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-exclamation-triangle"></i> Alertes système</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-warning">
                                            <i class="fas fa-clock"></i> Retard prévu sur Paris-Lyon 14:30
                                        </div>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i> Maintenance programmée dimanche
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Gares Section -->
                    <div id="gares" class="admin-section" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>Gestion des Gares</h4>
                            <button class="btn btn-primary" onclick="showAddGareModal()">
                                <i class="fas fa-plus"></i> Nouvelle Gare
                            </button>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Nom</th>
                                        <th>Ville</th>
                                        <th>Code</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>Gare de Lyon</td>
                                        <td>Paris</td>
                                        <td>PLY</td>
                                        <td><span class="badge bg-success">Active</span></td>
                                        <td class="table-actions">
                                            <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                            <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>Gare Part-Dieu</td>
                                        <td>Lyon</td>
                                        <td>LPD</td>
                                        <td><span class="badge bg-success">Active</span></td>
                                        <td class="table-actions">
                                            <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                            <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>Gare Saint-Charles</td>
                                        <td>Marseille</td>
                                        <td>MSC</td>
                                        <td><span class="badge bg-success">Active</span></td>
                                        <td class="table-actions">
                                            <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                            <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Trajets Section -->
                    <div id="trajets" class="admin-section" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>Gestion des Trajets</h4>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i> Nouveau Trajet
                            </button>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Départ</th>
                                        <th>Arrivée</th>
                                        <th>Distance</th>
                                        <th>Durée</th>
                                        <th>Prix</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>Paris</td>
                                        <td>Lyon</td>
                                        <td>462 km</td>
                                        <td>2h15</td>
                                        <td>45€</td>
                                        <td class="table-actions">
                                            <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                            <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>Lyon</td>
                                        <td>Marseille</td>
                                        <td>314 km</td>
                                        <td>1h40</td>
                                        <td>35€</td>
                                        <td class="table-actions">
                                            <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                            <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Voyages Section -->
                    <div id="voyages" class="admin-section" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>Programmation des Voyages</h4>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i> Programmer un Voyage
                            </button>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select class="form-select">
                                    <option>Tous les trajets</option>
                                    <option>Paris → Lyon</option>
                                    <option>Lyon → Marseille</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary">Filtrer</button>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date</th>
                                        <th>Trajet</th>
                                        <th>Départ</th>
                                        <th>Arrivée</th>
                                        <th>Places</th>
                                        <th>Réservées</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>29/05/2025</td>
                                        <td>Paris → Lyon</td>
                                        <td>08:30</td>
                                        <td>10:45</td>
                                        <td>120</td>
                                        <td>88</td>
                                        <td><span class="badge bg-success">Confirmé</span></td>
                                        <td class="table-actions">
                                            <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                            <button class="btn btn-sm btn-outline-warning">Annuler</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Other sections would be similar... -->
                    <div id="reservations" class="admin-section" style="display: none;">
                        <h4>Gestion des Réservations</h4>
                        <p class="text-muted">Section en cours de développement...</p>
                    </div>

                    <div id="utilisateurs" class="admin-section" style="display: none;">
                        <h4>Gestion des Utilisateurs</h4>
                        <p class="text-muted">Section en cours de développement...</p>
                    </div>

                    <div id="statistiques" class="admin-section" style="display: none;">
                        <h4>Statistiques et Rapports</h4>
                        <p class="text-muted">Section en cours de développement...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check admin access
        document.addEventListener('DOMContentLoaded', function() {
            const user = sessionStorage.getItem('user');
            if (!user) {
                alert('Accès non autorisé. Redirection vers la page de connexion.');
                window.location.href = 'login.html';
                return;
            }
            
            const userData = JSON.parse(user);
            if (!userData.isAdmin) {
                alert('Accès administrateur requis.');
                window.location.href = 'index.html';
                return;
            }
            
            updateTime();
            setInterval(updateTime, 1000);
        });

        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('fr-FR');
        }

        // Show section
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.admin-section').forEach(section => {
                section.style.display = 'none';
            });
            
            // Remove active class from all nav links
            document.querySelectorAll('.admin-sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionName).style.display = 'block';
            
            // Add active class to clicked nav link
            event.target.classList.add('active');
            
            // Update section title
            const titles = {
                'dashboard': 'Tableau de bord',
                'gares': 'Gestion des Gares',
                'trajets': 'Gestion des Trajets',
                'voyages': 'Programmation des Voyages',
                'reservations': 'Gestion des Réservations',
                'utilisateurs': 'Gestion des Utilisateurs',
                'statistiques': 'Statistiques et Rapports'
            };
            document.getElementById('sectionTitle').textContent = titles[sectionName];
        }

        // Logout function
        function logout() {
            sessionStorage.removeItem('user');
            alert('Déconnexion réussie');
            window.location.href = 'login.html';
        }

        // Show add gare modal (placeholder)
        function showAddGareModal() {
            alert('Fonctionnalité d\'ajout de gare : disponible dans la version complète avec base de données');
        }
    </script>
</body>
</html>

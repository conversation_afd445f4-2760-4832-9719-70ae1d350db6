<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Inscription</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .register-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 0;
        }
        .register-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .register-header {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .register-body {
            padding: 40px;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .btn-register {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
        }
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        .strength-weak { background: #dc3545; }
        .strength-medium { background: #ffc107; }
        .strength-strong { background: #28a745; }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="register-card">
                        <div class="register-header">
                            <h2><i class="fas fa-train"></i> TrainSystem</h2>
                            <p class="mb-0">Créez votre compte</p>
                        </div>
                        <div class="register-body">
                            <form id="registerForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="nom" class="form-label">
                                            <i class="fas fa-user text-muted"></i> Nom *
                                        </label>
                                        <input type="text" class="form-control" id="nom" required 
                                               placeholder="Votre nom">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="prenom" class="form-label">
                                            <i class="fas fa-user text-muted"></i> Prénom *
                                        </label>
                                        <input type="text" class="form-control" id="prenom" required 
                                               placeholder="Votre prénom">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope text-muted"></i> Adresse email *
                                    </label>
                                    <input type="email" class="form-control" id="email" required 
                                           placeholder="<EMAIL>">
                                    <div class="form-text">Nous ne partagerons jamais votre email</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="telephone" class="form-label">
                                        <i class="fas fa-phone text-muted"></i> Téléphone
                                    </label>
                                    <input type="tel" class="form-control" id="telephone" 
                                           placeholder="06 12 34 56 78">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="dateNaissance" class="form-label">
                                        <i class="fas fa-calendar text-muted"></i> Date de naissance
                                    </label>
                                    <input type="date" class="form-control" id="dateNaissance">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="motDePasse" class="form-label">
                                        <i class="fas fa-lock text-muted"></i> Mot de passe *
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="motDePasse" required 
                                               placeholder="Choisissez un mot de passe">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength" id="passwordStrength"></div>
                                    <div class="form-text">
                                        Le mot de passe doit contenir au moins 8 caractères
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirmationMotDePasse" class="form-label">
                                        <i class="fas fa-lock text-muted"></i> Confirmer le mot de passe *
                                    </label>
                                    <input type="password" class="form-control" id="confirmationMotDePasse" required 
                                           placeholder="Confirmez votre mot de passe">
                                    <div class="invalid-feedback" id="passwordMismatch">
                                        Les mots de passe ne correspondent pas
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="acceptConditions" required>
                                    <label class="form-check-label" for="acceptConditions">
                                        J'accepte les <a href="#" data-bs-toggle="modal" data-bs-target="#conditionsModal">conditions générales</a> *
                                    </label>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="newsletter">
                                    <label class="form-check-label" for="newsletter">
                                        Je souhaite recevoir les offres promotionnelles par email
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn btn-register text-white w-100 mb-3">
                                    <i class="fas fa-user-plus"></i> Créer mon compte
                                </button>
                            </form>
                            
                            <hr>
                            
                            <div class="text-center">
                                <p class="mb-2">Déjà un compte ?</p>
                                <a href="login.html" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt"></i> Se connecter
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="index.html" class="text-white text-decoration-none">
                            <i class="fas fa-arrow-left"></i> Retour à l'accueil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Conditions -->
    <div class="modal fade" id="conditionsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Conditions Générales d'Utilisation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>1. Objet</h6>
                    <p>Les présentes conditions générales régissent l'utilisation du service TrainSystem.</p>
                    
                    <h6>2. Inscription</h6>
                    <p>L'inscription est gratuite et nécessaire pour effectuer des réservations.</p>
                    
                    <h6>3. Protection des données</h6>
                    <p>Vos données personnelles sont protégées conformément au RGPD.</p>
                    
                    <h6>4. Responsabilité</h6>
                    <p>L'utilisateur est responsable de l'exactitude des informations fournies.</p>
                    
                    <h6>5. Modification</h6>
                    <p>Ces conditions peuvent être modifiées à tout moment avec préavis.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">J'accepte</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('motDePasse');
            const toggleIcon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        });

        // Password strength indicator
        document.getElementById('motDePasse').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');
            
            let strength = 0;
            if (password.length >= 8) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            strengthBar.className = 'password-strength';
            if (strength <= 2) {
                strengthBar.classList.add('strength-weak');
            } else if (strength <= 3) {
                strengthBar.classList.add('strength-medium');
            } else {
                strengthBar.classList.add('strength-strong');
            }
        });

        // Password confirmation validation
        function validatePasswordMatch() {
            const password = document.getElementById('motDePasse').value;
            const confirmation = document.getElementById('confirmationMotDePasse').value;
            const confirmField = document.getElementById('confirmationMotDePasse');
            const mismatchDiv = document.getElementById('passwordMismatch');
            
            if (confirmation && password !== confirmation) {
                confirmField.classList.add('is-invalid');
                mismatchDiv.style.display = 'block';
                return false;
            } else {
                confirmField.classList.remove('is-invalid');
                mismatchDiv.style.display = 'none';
                return true;
            }
        }

        document.getElementById('confirmationMotDePasse').addEventListener('input', validatePasswordMatch);
        document.getElementById('motDePasse').addEventListener('input', validatePasswordMatch);

        // Form submission
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!validatePasswordMatch()) {
                return false;
            }
            
            const formData = {
                nom: document.getElementById('nom').value,
                prenom: document.getElementById('prenom').value,
                email: document.getElementById('email').value,
                telephone: document.getElementById('telephone').value,
                dateNaissance: document.getElementById('dateNaissance').value,
                newsletter: document.getElementById('newsletter').checked
            };
            
            // Simulate registration
            showMessage('Inscription réussie ! Redirection vers la page de connexion...', 'success');
            
            // Store user data (in real app, this would be sent to server)
            localStorage.setItem('registeredUser', JSON.stringify(formData));
            
            // Redirect to login page
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
        });

        // Show message function
        function showMessage(message, type) {
            // Remove existing alerts
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }
            
            // Create new alert
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // Insert alert before form
            const form = document.getElementById('registerForm');
            form.parentNode.insertBefore(alert, form);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // Set max date for birth date (18 years ago)
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const maxDate = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
            document.getElementById('dateNaissance').max = maxDate.toISOString().split('T')[0];
        });
    </script>
</body>
</html>

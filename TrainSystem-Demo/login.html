<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Connexion</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .login-body {
            padding: 40px;
        }
        .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
        }
        .demo-accounts {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="login-card">
                        <div class="login-header">
                            <h2><i class="fas fa-train"></i> TrainSystem</h2>
                            <p class="mb-0">Connectez-vous à votre compte</p>
                        </div>
                        <div class="login-body">
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope text-muted"></i> Adresse email
                                    </label>
                                    <input type="email" class="form-control" id="email" required 
                                           placeholder="<EMAIL>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="motDePasse" class="form-label">
                                        <i class="fas fa-lock text-muted"></i> Mot de passe
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="motDePasse" required 
                                               placeholder="Votre mot de passe">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="seSouvenir">
                                    <label class="form-check-label" for="seSouvenir">
                                        Se souvenir de moi
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn btn-login text-white w-100 mb-3">
                                    <i class="fas fa-sign-in-alt"></i> Se connecter
                                </button>
                            </form>
                            
                            <div class="text-center">
                                <a href="#" class="text-decoration-none">Mot de passe oublié ?</a>
                            </div>
                            
                            <hr>
                            
                            <div class="text-center">
                                <p class="mb-2">Pas encore de compte ?</p>
                                <a href="register.html" class="btn btn-outline-primary">
                                    <i class="fas fa-user-plus"></i> Créer un compte
                                </a>
                            </div>
                            
                            <!-- Comptes de démonstration -->
                            <div class="demo-accounts">
                                <h6><i class="fas fa-info-circle text-primary"></i> Comptes de démonstration</h6>
                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <button type="button" class="btn btn-sm btn-outline-success w-100" 
                                                onclick="fillDemo('<EMAIL>', 'password')">
                                            <i class="fas fa-user-shield"></i> Administrateur
                                        </button>
                                    </div>
                                    <div class="col-12">
                                        <button type="button" class="btn btn-sm btn-outline-info w-100" 
                                                onclick="fillDemo('<EMAIL>', 'password')">
                                            <i class="fas fa-user"></i> Client
                                        </button>
                                    </div>
                                </div>
                                <small class="text-muted d-block mt-2">
                                    Cliquez pour remplir automatiquement les champs
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="index.html" class="text-white text-decoration-none">
                            <i class="fas fa-arrow-left"></i> Retour à l'accueil
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('motDePasse');
            const toggleIcon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        });

        // Fill demo credentials
        function fillDemo(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('motDePasse').value = password;
            
            // Add visual feedback
            const form = document.getElementById('loginForm');
            form.classList.add('border', 'border-success');
            setTimeout(() => {
                form.classList.remove('border', 'border-success');
            }, 1000);
        }

        // Form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('motDePasse').value;
            
            // Simulate authentication
            if (email === '<EMAIL>' && password === 'password') {
                // Store user session
                sessionStorage.setItem('user', JSON.stringify({
                    email: email,
                    nom: 'Administrateur',
                    prenom: 'Admin',
                    isAdmin: true
                }));
                
                // Show success message
                showMessage('Connexion réussie ! Redirection vers l\'administration...', 'success');
                
                // Redirect to admin page
                setTimeout(() => {
                    window.location.href = 'admin.html';
                }, 1500);
                
            } else if (email === '<EMAIL>' && password === 'password') {
                // Store user session
                sessionStorage.setItem('user', JSON.stringify({
                    email: email,
                    nom: 'Dupont',
                    prenom: 'Jean',
                    isAdmin: false
                }));
                
                // Show success message
                showMessage('Connexion réussie ! Redirection vers l\'accueil...', 'success');
                
                // Redirect to home page
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
                
            } else {
                showMessage('Email ou mot de passe incorrect. Utilisez les comptes de démonstration.', 'danger');
            }
        });

        // Show message function
        function showMessage(message, type) {
            // Remove existing alerts
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }
            
            // Create new alert
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // Insert alert before form
            const form = document.getElementById('loginForm');
            form.parentNode.insertBefore(alert, form);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // Check if user is already logged in
        document.addEventListener('DOMContentLoaded', function() {
            const user = sessionStorage.getItem('user');
            if (user) {
                const userData = JSON.parse(user);
                showMessage(`Vous êtes déjà connecté en tant que ${userData.prenom}. Redirection...`, 'info');
                setTimeout(() => {
                    window.location.href = userData.isAdmin ? 'admin.html' : 'index.html';
                }, 2000);
            }
        });
    </script>
</body>
</html>

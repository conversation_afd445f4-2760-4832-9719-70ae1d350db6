<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Accueil</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .navbar-brand {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search.html">Rechercher</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html">Connexion</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="register.html">Inscription</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">Bienvenue sur TrainSystem</h1>
            <p class="lead mb-5">Votre plateforme de réservation de billets de train en ligne</p>
            
            <!-- Status de l'application -->
            <div class="alert alert-success d-inline-block mb-4">
                <i class="fas fa-check-circle me-2"></i>
                Application déployée avec succès !
            </div>
            
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-dark">Rechercher un trajet</h5>
                            <form action="search-results.html" method="get">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="villeDepart" placeholder="Ville de départ" required>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="villeArrivee" placeholder="Ville d'arrivée" required>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="date" class="form-control" name="dateVoyage" required>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-search"></i> Rechercher
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Fonctionnalités disponibles</h2>
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="card h-100 text-center feature-card">
                        <div class="card-body">
                            <i class="fas fa-user-plus fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Inscription</h5>
                            <p class="card-text">Créez votre compte pour accéder à toutes les fonctionnalités</p>
                            <a href="register.html" class="btn btn-outline-success">Tester</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 text-center feature-card">
                        <div class="card-body">
                            <i class="fas fa-sign-in-alt fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">Connexion</h5>
                            <p class="card-text">Connectez-vous à votre espace personnel</p>
                            <a href="login.html" class="btn btn-outline-warning">Tester</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 text-center feature-card">
                        <div class="card-body">
                            <i class="fas fa-search fa-3x text-info mb-3"></i>
                            <h5 class="card-title">Recherche</h5>
                            <p class="card-text">Trouvez le voyage qui vous convient</p>
                            <a href="search.html" class="btn btn-outline-info">Tester</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card h-100 text-center feature-card">
                        <div class="card-body">
                            <i class="fas fa-cog fa-3x text-danger mb-3"></i>
                            <h5 class="card-title">Administration</h5>
                            <p class="card-text">Gérez les trajets et réservations</p>
                            <a href="admin.html" class="btn btn-outline-danger">Tester</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Info Section -->
    <section class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h3><i class="fas fa-info-circle text-primary"></i> Informations de test</h3>
                    <div class="card">
                        <div class="card-body">
                            <h6>Compte administrateur :</h6>
                            <p class="mb-1"><strong>Email :</strong> <EMAIL></p>
                            <p class="mb-3"><strong>Mot de passe :</strong> password</p>
                            
                            <h6>Villes disponibles :</h6>
                            <p class="mb-0">Paris, Lyon, Marseille, Bordeaux, Toulouse, Nice, Nantes, Strasbourg</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h3><i class="fas fa-database text-success"></i> Données de démonstration</h3>
                    <div class="card">
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> 8 gares configurées</li>
                                <li><i class="fas fa-check text-success"></i> 16 trajets bidirectionnels</li>
                                <li><i class="fas fa-check text-success"></i> Voyages programmés pour 7 jours</li>
                                <li><i class="fas fa-check text-success"></i> Système de réservation fonctionnel</li>
                                <li><i class="fas fa-check text-success"></i> Interface d'administration complète</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">© 2025 TrainSystem - Application JEE Complète</p>
            <p class="mb-0">
                <i class="fas fa-code"></i> Développé avec Java JEE, Bootstrap et MySQL
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set today's date as default for date input
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.querySelector('input[name="dateVoyage"]');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.value = today;
            }
        });
    </script>
</body>
</html>
